package ar.gob.lapampa.nsl.seguridad.repositories;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.seguridad.entities.LiqParametro;

@Repository
public interface LiqParametroRepository
    extends JpaRepository<LiqParametro, Long>, QuerydslPredicateExecutor<LiqParametro> {

  Optional<LiqParametro> findByNombre(String nombre);
}
