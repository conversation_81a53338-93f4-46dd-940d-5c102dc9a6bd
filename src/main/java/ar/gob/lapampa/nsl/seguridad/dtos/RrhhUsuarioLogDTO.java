package ar.gob.lapampa.nsl.seguridad.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import com.querydsl.core.annotations.QueryProjection;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RrhhUsuarioLogDTO extends BaseRRHHDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 6825200916191992407L;
  private String login;
  private String tipo;
  private String estado;
  private String resultado;
  private Date creado;

  public RrhhUsuarioLogDTO(String login) {
    super();
    this.login = login;
  }

  @QueryProjection
  public RrhhUsuarioLogDTO(Long id) {
    super();
    this.id = id;
  }

}

