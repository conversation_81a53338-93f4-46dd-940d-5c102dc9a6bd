package ar.gob.lapampa.nsl.seguridad.services;

import jakarta.validation.Valid;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.seguridad.dtos.GamCredentialsDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.GamUserInfoDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.ServiceNameRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.TokenRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.TokenResponseDTO;

@Transactional
public interface GamUserService {

  GenericResponseDTO validar(GamCredentialsDTO signUpRequest);

  GenericResponseDTO menusByToken(@Valid TokenRequestDTO request);

  String getLoginUrl();

  String getWelcomeUrl();

  GamUserInfoDTO getUserInfo(String token);

  TokenResponseDTO getTokenByRefresh(String refreshToken);

  String getLogoutUrl(String token);

  GenericResponseDTO rolesByLogin(@Valid String token);

  UserDetails loadUserByToken(String token);

  boolean signoutUserBySessionId(Long sessionId);

  boolean signoutUserByToken(String token);

  String agregarServiceNameByToken(@Valid ServiceNameRequestDTO request);

}
