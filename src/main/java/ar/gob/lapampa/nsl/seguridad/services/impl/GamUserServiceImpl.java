package ar.gob.lapampa.nsl.seguridad.services.impl;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;
import ar.gob.lapampa.nsl.seguridad.configs.GamRestConfig;
import ar.gob.lapampa.nsl.seguridad.dtos.GamCredentialsDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.GamUserInfoDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.ServiceNameRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.TokenRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.TokenResponseDTO;
import ar.gob.lapampa.nsl.seguridad.entities.UserSession;
import ar.gob.lapampa.nsl.seguridad.jwt.JwtUtils;
import ar.gob.lapampa.nsl.seguridad.repositories.UserSessionRepository;
import ar.gob.lapampa.nsl.seguridad.services.GamUserService;
import ar.gob.lapampa.nsl.seguridad.services.RedisUserDetailsService;
import ar.gob.lapampa.nsl.seguridad.services.RrhhUsuarioService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

@Service
@Transactional
@Slf4j
public class GamUserServiceImpl implements GamUserService {

	@Value("${spring.profiles.active}")
	private String activeProfile;

	@Autowired
	private Random random;

	@Autowired
	GamRestConfig gamRestConfig;

	@Autowired
	private RrhhUsuarioService rrhhUsuarioService;

	@Autowired
	private RedisUserDetailsService userDetailsService;

	@Autowired
	private UserSessionRepository userSessionRepository;

	@Autowired
	private JwtUtils jwtUtils;

	@Override
	public GenericResponseDTO validar(GamCredentialsDTO signUpRequest) {
		GenericResponseDTO response = new GenericResponseDTO();
		String params;
		ObjectMapper objectMapper = new ObjectMapper();
		TokenResponseDTO dto;

		if (signUpRequest.getCode().isEmpty() || signUpRequest.getGam().isEmpty()) {
			log.error("Credenciales Vacías: {}", signUpRequest);
			return null;
		}

		String base_url = gamRestConfig.getBaseUrl(activeProfile);
		HashMap<String, String> map = new LinkedHashMap<>();
		map.put("code", signUpRequest.getCode());
		map.put("client_id", gamRestConfig.getClient_id());
		map.put("client_secret", gamRestConfig.getClientSecret(activeProfile));
		map.put("redirect_uri", gamRestConfig.getRedirectUrl(activeProfile));
		map.put("grant_type", "authorization_code");

		try {
			params = this.getDataString(map);
		} catch (UnsupportedEncodingException e) {
			log.error("Error al armar parámetros: {}", e.getMessage(), e);
			return null;
		}

		HttpURLConnection connection = null;
		String responseContent;

		try {
			String fullPath = "%s/oauth/gam/access_token".formatted(base_url);
			URL url = URI.create(fullPath).toURL();
			connection = (HttpURLConnection) url.openConnection();
			connection.setDoOutput(true);
			connection.setInstanceFollowRedirects(true);
			connection.setRequestMethod("POST");

			byte[] postData = params.getBytes(StandardCharsets.UTF_8);
			connection.setRequestProperty("Content-Length", Integer.toString(postData.length));
			connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

			// Escribir datos
			try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
				wr.write(postData);
				wr.flush();
			}

			// Leer respuesta
			int responseCode = connection.getResponseCode();
			if (responseCode == HttpURLConnection.HTTP_OK) {
				responseContent = readResponse(connection.getInputStream());
			} else {
				responseContent = readResponse(connection.getErrorStream());
				log.error("Error en la respuesta del token. Code: {} Response: {}", responseCode, responseContent);
				return null;
			}

			// Parsear respuesta
			dto = objectMapper.readValue(responseContent, TokenResponseDTO.class);
			String username = this.getUsernameFromTokenStr(dto.getAccess_token());
			rrhhUsuarioService.createSession(dto, username);
			response.setEstadoExito(dto);
			return response;

		} catch (Exception e) {
			log.error("Error procesando la solicitud: {}", e.getMessage(), e);
			return null;
		} finally {
			if (connection != null) {
				connection.disconnect();
			}
		}
	}

	private String getDataString(HashMap<String, String> params) throws UnsupportedEncodingException {
		StringBuilder result = new StringBuilder();
		boolean first = true;
		for (Map.Entry<String, String> entry : params.entrySet()) {
			if (first)
				first = false;
			else
				result.append("&");
			result.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8));
			result.append("=");
			result.append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8));
		}
		return result.toString();
	}

	/*
	 * Recibe token externo y devuelve token JWT + menus
	 */
	public GenericResponseDTO menusByToken(@Valid TokenRequestDTO request) {
		final GenericResponseDTO response = new GenericResponseDTO();
		String username = this.getUsernameFromToken(request);

		if (Objects.nonNull(username)) {
			log.info("Obteniendo menús para usuario: {}", username);
			return rrhhUsuarioService.menusByToken(request, username);
		}

		log.error("Token inválido o usuario no encontrado para external token: {}", request.getToken());
		response.setEstadoError("No se encontró el usuario o external token no válido");
		return response;
	}

	private String getUsernameFromTokenStr(@Valid String token) {
		GamUserInfoDTO dto = this.getUserInfo(token);
		if (Objects.nonNull(dto)) {
			return dto.getUsuario();
		}
		return null;
	}

	private String getUsernameFromToken(@Valid TokenRequestDTO request) {
		String token = request.getToken();
		// Si es token externo
		if (token.contains("!")) {
			GamUserInfoDTO dto = this.getUserInfo(token);

			if (Objects.nonNull(dto) && Objects.nonNull(dto.getUsuario())) {
				log.info("Usuario encontrado en token: {}", dto.getUsuario());
				return dto.getUsuario(); // cuit
			}
		} else {
			// Token JWT
			return jwtUtils.getUserNameFromJwtToken(token); // cuit
		}

		log.error("No se pudo obtener usuario del token");
		return null;
	}

	@Override
	public String getLoginUrl() {
		String baseUrl = gamRestConfig.getBaseUrl(activeProfile);
		String clientId = gamRestConfig.getClient_id();
		String redirectUrl = gamRestConfig.getRedirectUrl(activeProfile);
		String state = this.randomString();

		return String.format("%s/oauth/gam/signin?oauth=auth&scope=gam_user_data&client_id=%s&redirect_uri=%s&state=%s",
				baseUrl, clientId, redirectUrl, state);
	}

	private String buildRequestUrl(String baseUrl, String endpoint) {
		return String.format("%s/%s", baseUrl, endpoint);
	}

	private String randomString() {
		int leftLimit = 97; // letter 'a'
		int rightLimit = 122; // letter 'z'
		int targetStringLength = 40;

		return random.ints(leftLimit, rightLimit + 1).limit(targetStringLength)
				.collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append).toString();
	}

	@Override
	public String getWelcomeUrl() {
		return gamRestConfig.getWelcomeUrl(activeProfile);
	}

	/*
	 * Obtiene los datos del usuario en GAM con token GAM (userInfo)
	 */
	@Override
	public GamUserInfoDTO getUserInfo(String token) {
		if (token == null || token.trim().isEmpty()) {
			log.error("Token vacío o nulo");
			return null;
		}

		ObjectMapper objectMapper = new ObjectMapper();
		String baseUrl = gamRestConfig.getBaseUrl(activeProfile);
		HttpURLConnection connection = null;
		String requestUrl = "%s/oauth/gam/userinfo".formatted(baseUrl);

		try {
			URL url = URI.create(requestUrl).toURL();
			connection = (HttpURLConnection) url.openConnection();
			connection.setDoOutput(false);
			connection.setInstanceFollowRedirects(true);
			connection.setRequestMethod("GET");

			// Usar el token exactamente como viene
			connection.setRequestProperty("Authorization", token);

			log.debug("Making request to URL: {} with Authorization header", requestUrl);

			connection.connect();

			int responseCode = connection.getResponseCode();
			log.debug("Response code from GAM: {}", responseCode);

			String response;
			if (responseCode == HttpURLConnection.HTTP_OK) {
				response = readResponse(connection.getInputStream());
				log.debug("GAM successful response: {}", response);
				GamUserInfoDTO dto = objectMapper.readValue(response, GamUserInfoDTO.class);
				if (dto != null && dto.isValid()) {
					return dto;
				}
			} else {
				response = readResponse(connection.getErrorStream());
				log.error("GAM error response: {} (code: {})", response, responseCode);
			}

			return null;

		} catch (Exception e) {
			log.error("Error getting user info from GAM: {}", e.getMessage(), e);
			return null;
		} finally {
			if (connection != null) {
				connection.disconnect();
			}
		}
	}

	private String readResponse(InputStream stream) throws IOException {
		if (stream == null)
			return "";

		try (BufferedReader reader = new BufferedReader(new InputStreamReader(stream))) {
			return reader.lines().collect(Collectors.joining("\n"));
		}
	}

	@Override
	public TokenResponseDTO getTokenByRefresh(String refreshToken) {
		if (!Objects.nonNull(refreshToken)) {
			log.error("Token Vacío: {}", refreshToken);
			return null;
		}

		// find user session in redis
		List<UserSession> userSessionList = userSessionRepository.findByExternalRefresh(refreshToken);
		if (userSessionList.isEmpty()) {
			log.error("No se encontró sesión en Redis para refresh token");
			return null;
		}

		UserSession userSession = userSessionList.getFirst();
		TokenResponseDTO dto;
		ObjectMapper objectMapper = new ObjectMapper();
		String baseUrl = gamRestConfig.getBaseUrl(activeProfile);

		HashMap<String, String> map = new LinkedHashMap<>();
		map.put("grant_type", "refresh_token");
		map.put("client_id", gamRestConfig.getClient_id());
		map.put("client_secret", gamRestConfig.getClientSecret(activeProfile));
		map.put("refresh_token", refreshToken);

		String params;
		try {
			params = this.getDataString(map);
		} catch (UnsupportedEncodingException e) {
			log.error("Error al armar parámetros: {}", e.getMessage(), e);
			return null;
		}

		// Realizar petición a GAM
		HttpURLConnection connection = null;
		String responseContent;

		try {
			String fullPath = "%s/oauth/gam/access_token".formatted(baseUrl);
			URL url = URI.create(fullPath).toURL();
			connection = (HttpURLConnection) url.openConnection();
			connection.setDoOutput(true);
			connection.setInstanceFollowRedirects(true);
			connection.setRequestMethod("POST");

			byte[] postData = params.getBytes(StandardCharsets.UTF_8);
			connection.setRequestProperty("Content-Length", Integer.toString(postData.length));
			connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

			// Escribir datos
			try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
				wr.write(postData);
				wr.flush();
			}

			// Leer respuesta
			int responseCode = connection.getResponseCode();
			if (responseCode == HttpURLConnection.HTTP_OK) {
				responseContent = readResponse(connection.getInputStream());
				dto = objectMapper.readValue(responseContent, TokenResponseDTO.class);

				// Actualizar sesión en Redis
				userSession.setCallbackDate(new Date().getTime());
				userSession.setToken(dto.getAccess_token());
				userSession.setRefresh(dto.getRefresh_token());
				userSessionRepository.save(userSession);

				return dto;
			} else {
				responseContent = readResponse(connection.getErrorStream());
				log.error("Error en la respuesta del token. Code: {} Response: {}", responseCode, responseContent);
				return null;
			}

		} catch (Exception e) {
			log.error("Error en refresh token: {}", e.getMessage(), e);
			return null;
		} finally {
			if (connection != null) {
				connection.disconnect();
			}
		}
	}

	@Override
	public String getLogoutUrl(String token) {

		String params;
		HashMap<String, String> map = new LinkedHashMap<>();
		map.put("oauth", "signout");
		map.put("client_id", gamRestConfig.getClient_id());
		map.put("client_secret", gamRestConfig.getClientSecret(activeProfile));
		map.put("server_ip", "1");
		map.put("redirect_uri", gamRestConfig.getLogOutUrl(activeProfile));
		map.put("token", token);
		map.put("first_call", "1");
		map.put("state", "state");

		try {
			params = this.getDataString(map);
		} catch (UnsupportedEncodingException e) {
			log.error("Error al armar parámetros:", e);
			return null;
		}
		return "%s/oauth/gam/signout?%s".formatted(gamRestConfig.getBaseUrl(activeProfile), params);
	}

	@Override
	public GenericResponseDTO rolesByLogin(@Valid String token) {
		TokenRequestDTO request = new TokenRequestDTO();
		request.setToken(token);
		request.setRoleId(null);
		// username es el cuit y aqui lo buscamos a GAM con el token
		String username = this.getUsernameFromToken(request);
		// aqui obtenemos el listado de roles de usuario a partir del cuit
		return rrhhUsuarioService.rolesByLogin(username);
	}

	@Override
	public UserDetails loadUserByToken(String token) {
		return userDetailsService.loadUserByUsername(token);
	}

	@Override
	public boolean signoutUserByToken(String gamToken) {

		List<UserSession> listUserSessions = userSessionRepository.findByExternalToken(gamToken);

		if (listUserSessions.isEmpty()) {
			return false;
		}
		userSessionRepository.deleteAll(listUserSessions);
		return true;
	}

	@Override
	public boolean signoutUserBySessionId(Long sessionId) {
		Optional<UserSession> userSession = userSessionRepository.findById(sessionId);
		if (userSession.isPresent()) {
			userSessionRepository.delete(userSession.get());
			return true;
		} else {
			return false;
		}
	}

	@Override
	public String agregarServiceNameByToken(@Valid ServiceNameRequestDTO request) {
		String response = "Sesión no encontrada";
		List<UserSession> userSessionList = userSessionRepository.findByToken(request.getToken());
		if (userSessionList.size() == 1) {
			UserSession userSession = userSessionList.getFirst();
			if (!"null_null".equals(request.getServiceName())) {
				userSession.getServicename().add(request.getServiceName());
			}
			userSessionRepository.save(userSession);
			response = userSession.getServicename().toString();
		}
		return response;
	}

}
