package ar.gob.lapampa.nsl.seguridad.controllers;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.seguridad.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.JwtRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.ServiceNameRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.TokenRequestDTO;
import ar.gob.lapampa.nsl.seguridad.services.GamUserService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Main Application Controller Defines default check end points
 * 
 * @version 1.0.0
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@NoArgsConstructor
@Slf4j
@RequestMapping("/api/v2/app/credenciales")

public class GamSeguridadController {
  /**
   * Valida credenciales de usuario
   *
   */


  @Autowired
  private GamUserService gamUserService;

  @Operation(summary = "Validar Credenciales", description = "Válida credenciales del usuario",
      tags = {"Validar"}, hidden = false, deprecated = true)
  @Transactional
  @PostMapping("/findByLogin")
  public GenericResponseDTO findByLogin(@Valid @RequestBody TokenRequestDTO request) {
    log.info("Ingreso a findByLogin... ");
    return gamUserService.menusByToken(request);
  }


  /**
   * Valida credenciales de usuario
   * 
   * @return String
   */
  @Operation(summary = "Obtiene Roles del Usuario por su Login",
      description = "Válida credenciales del usuario", tags = {"Validar"}, hidden = false,
      deprecated = false)
  @Transactional
  @GetMapping("/rolesByLogin")
  public GenericResponseDTO rolesByLogin(@Valid @RequestParam String token) {
    log.info("Ingreso a rolesByLogin... ");
    return gamUserService.rolesByLogin(token);
  }

  @PostMapping("/userDetails")
  public UserDetails getUserDetails(@Valid @RequestBody JwtRequestDTO request) {
    log.info("Ingreso a userDetails... ");
    return gamUserService.loadUserByToken(request.getToken());
  }

  @Operation(summary = "Obtiene UserDetails y menus asignados",
      description = "Obtiene menus y permisos del usuario", tags = {"Validar"}, hidden = false,
      deprecated = false)
  @Transactional
  @PostMapping("/menusByToken")
  public GenericResponseDTO menusByToken(@Valid @RequestBody TokenRequestDTO request) {
    log.info("Ingreso a menusByToken... ");
    return gamUserService.menusByToken(request);
  }

  @Operation(summary = "Obtiene y agrega service names",
      description = "Obtiene y agrega services names path del usuario", tags = {"Validar"},
      hidden = false, deprecated = false)
  @Transactional
  @PostMapping("/serviceNameByToken")
  public String agregarServiceNameByToken(@Valid @RequestBody ServiceNameRequestDTO request) {
    log.info("Ingreso a serviceNameByToken... ");
    return gamUserService.agregarServiceNameByToken(request);
  }
}
