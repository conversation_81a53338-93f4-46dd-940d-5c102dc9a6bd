package ar.gob.lapampa.nsl.seguridad.repositories;

import ar.gob.lapampa.nsl.seguridad.entities.UserSession;
import ar.gob.lapampa.nsl.seguridad.utils.Constantes;
import ar.gob.lapampa.nsl.seguridad.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.core.RedisCallback;

@Slf4j
public class UserSessionRepositoryImpl implements UserSessionRepositoryCustom {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void safeDelete(UserSession session) {
        if (session == null || session.getId() == null) {
            return;
        }
        
        redisTemplate.execute((RedisCallback<Void>) connection -> {
            try {
                String sessionKey = Constantes.REDIS_SESSION_PREFIX + session.getId();
                byte[] sessionKeyBytes = sessionKey.getBytes();
                
                // Buscar claves relacionadas
                Set<byte[]> relatedKeys = connection.keys(("*:" + session.getId() + ":*").getBytes());
                
                if (relatedKeys != null && !relatedKeys.isEmpty()) {
                    // Eliminar claves relacionadas
                    byte[][] keysArray = relatedKeys.toArray(new byte[0][]);
                    connection.del(keysArray);
                }
                
                // Eliminar clave principal
                connection.del(sessionKeyBytes);
                
            } catch (Exception e) {
                log.error("Error during Redis delete operation: {}", e.getMessage());
            }
            return null;
        });
    }

    @Override
    public void safeDeleteById(Long id) {
        if (id == null) {
            return;
        }

        redisTemplate.execute((RedisCallback<Void>) connection -> {
            try {
                String sessionKey = Constantes.REDIS_SESSION_PREFIX + id;
                byte[] sessionKeyBytes = sessionKey.getBytes();
                
                // Buscar claves relacionadas
                Set<byte[]> relatedKeys = connection.keys(("*:" + id + ":*").getBytes());
                
                if (relatedKeys != null && !relatedKeys.isEmpty()) {
                    // Eliminar claves relacionadas
                    byte[][] keysArray = relatedKeys.toArray(new byte[0][]);
                    connection.del(keysArray);
                }
                
                // Eliminar clave principal
                connection.del(sessionKeyBytes);
                
            } catch (Exception e) {
                log.error("Error during Redis delete operation: {}", e.getMessage());
            }
            return null;
        });
    }

    @Override
    public void setExpire(String key, long seconds) {
        redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            try {
                return connection.expire(key.getBytes(), seconds);
            } catch (Exception e) {
                log.error("Error setting expiration for key {}: {}", key, e.getMessage());
                return false;
            }
        });
    }

    @Override
    public void setDefaultExpiration(String key) {
        setExpire(key, Constantes.REDIS_TTL_SECONDS);
    }
}