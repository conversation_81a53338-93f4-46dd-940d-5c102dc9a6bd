package ar.gob.lapampa.nsl.seguridad.services;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhAccionDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhRolDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhUsuarioDTO;
import ar.gob.lapampa.nsl.seguridad.entities.RrhhUsuario;
import ar.gob.lapampa.nsl.seguridad.entities.UserSession;
import ar.gob.lapampa.nsl.seguridad.mappers.RrhhUsuarioMapper;
import ar.gob.lapampa.nsl.seguridad.repositories.RrhhUsuarioRepository;
import ar.gob.lapampa.nsl.seguridad.repositories.UserSessionRepository;
import ar.gob.lapampa.nsl.seguridad.services.impl.UserDetailsImpl;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RedisUserDetailsService implements UserDetailsService {

  @Autowired
  private UserSessionRepository userSessionRepository;

  @Autowired
  private RrhhUsuarioRepository rrhhUsuarioRepository;

  List<SimpleGrantedAuthority> listaRoles = new ArrayList<>();
  List<SimpleGrantedAuthority> listaAcciones = new ArrayList<>();

  @Override
  @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
  // @Cacheable(value = "session", key = "#username", cacheManager = "cacheManager")
  public UserDetails loadUserByUsername(String token) throws UsernameNotFoundException {

    if (token == null || token.trim().isEmpty()) {
      throw new UsernameNotFoundException("Username no puede ser null o vacío");
    }

    List<UserSession> listUserSessions = null;

    // Si el token contiene '!' suponemos un token GAM
    if (token.contains("!")) {
      listUserSessions = userSessionRepository.findByExternalToken(token);
    } else {
      // Continuar con la lógica JWT token
      listUserSessions = userSessionRepository.findByToken(token);
    }

    if (listUserSessions.size() > 1) {
      log.debug("El token no es único no es válido: {}", token);
    }

    if (listUserSessions.isEmpty()) {
      log.error("Usuario no encontrado con token: {}", token);
      throw new UsernameNotFoundException("No hallado: %s".formatted(token));
    }

    // Aca busca de todas las sesiones la mas nueva
    // UserSession userSession = listUserSessions.stream()
    // .max(Comparator.comparing(UserSession::getCreated))
    // .orElseThrow(() -> new UsernameNotFoundException("No hallado:
    // %s".formatted(username)));

    UserSession userSession = listUserSessions.getFirst();

    log.debug("Encontrada session de usuario con ID: {}", userSession.getId());

    if (userSession.getRoles().isEmpty()) {
      log.info("Session de usuario sin roles, cargando desde RRHH");
      RrhhUsuario rrhhUser = rrhhUsuarioRepository.findByLogin(userSession.getCuil().toString());
      RrhhUsuarioDTO rrhhUserDto = RrhhUsuarioMapper.INSTANCE.rrhhUsuarioToRrhhUsuarioDto(rrhhUser);
      listaRoles =
          rrhhUserDto.getRoles().stream().filter(rol -> rol.getId() == userSession.getRoleId())
              .map(rol -> new SimpleGrantedAuthority(normalizeRole(rol))).toList();
      userSession.setRoles(new HashSet<>(listaRoles));
      userSession.getRoles().addAll(listaAcciones);
      userSessionRepository.save(userSession);
      log.info("Actualizada session de usuario con {} roles y {} actions", listaRoles.size(),
          listaAcciones.size());
    }

    log.debug("Construyendo UserDetails para session ID: {}", userSession.getId());
    return UserDetailsImpl.build(userSession);
  }

  private String normalizeRole(RrhhRolDTO rol) {
    String myStr = rol.getNombre();
    List<SimpleGrantedAuthority> acciones =
        rol.getAcciones().stream().filter(accion -> accion.getNombre().contains("NNSL_"))
            .map(accion -> new SimpleGrantedAuthority(normalizeAuthority(accion))).toList();
    listaAcciones.addAll(acciones);
    return "ROLE_%s".formatted(myStr.trim().toUpperCase().replace(' ', '_'));
  }

  private String normalizeAuthority(RrhhAccionDTO accion) {
    String myStr = accion.getNombre();
    return myStr.trim().toUpperCase().replace(' ', '_');
  }

  public boolean esJWT(String token) {
    return token != null && token.chars().filter(ch -> ch == '.').count() == 2;
  }

}
