package ar.gob.lapampa.nsl.seguridad.services.impl;

import ar.gob.lapampa.nsl.seguridad.services.RedisCleanupService;
import ar.gob.lapampa.nsl.seguridad.utils.Constantes;
import ar.gob.lapampa.nsl.seguridad.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RedisCleanupServiceImpl implements RedisCleanupService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Scheduled(fixedRate = 3600000) // Ejecutar cada hora
    @Override
    public void enforceExpiration() {
        try {
            Set<String> keys = redisTemplate.keys(Constantes.REDIS_SESSION_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                cleanExpiredSessions(keys);
                cleanOrphanedPhantoms();
                log.info("Redis cleanup completed successfully");
            }
        } catch (Exception e) {
            log.error("Error during Redis cleanup: {}", e.getMessage());
            throw new RuntimeException("Failed to perform Redis cleanup", e);
        }
    }

    private void cleanExpiredSessions(Set<String> keys) {
        keys.stream()
            .filter(key -> key.startsWith(Constantes.REDIS_SESSION_PREFIX))
            .forEach(sessionKey -> {
                try {
                    Long ttl = redisTemplate.getExpire(sessionKey);
                    // Solo procesar si el TTL es -1 (sin expiración) o -2 (ya expirado)
                    if (ttl != null && (ttl == -1 || ttl == -2)) {
                        if (ttl == -1) {
                            // Si no tiene expiración, establecer el TTL predeterminado
                            RedisUtils.safeExpire(redisTemplate, sessionKey, 
                                Constantes.REDIS_TTL_SECONDS, TimeUnit.SECONDS);
                            log.debug("Set expiration for key: {}", sessionKey);
                        } else if (ttl == -2) {
                            // Si ya expiró, limpiar sus datos relacionados
                            cleanRelatedData(sessionKey);
                        }
                    }
                } catch (Exception e) {
                    log.error("Error processing session key {}: {}", sessionKey, e.getMessage());
                }
            });
    }

    private void cleanRelatedData(String sessionKey) {
        try {
            String baseKey = sessionKey.substring(Constantes.REDIS_SESSION_PREFIX.length());
            Set<String> relatedKeys = redisTemplate.keys("*:" + baseKey + ":*");
            if (relatedKeys != null && !relatedKeys.isEmpty()) {
                RedisUtils.safeDeleteKeys(redisTemplate, relatedKeys);
                log.debug("Cleaned related data for expired session: {}", sessionKey);
            }
            RedisUtils.safeDelete(redisTemplate, sessionKey);
        } catch (Exception e) {
            log.error("Error cleaning related data for key {}: {}", sessionKey, e.getMessage());
        }
    }

    private void cleanOrphanedPhantoms() {
        try {
            Set<String> phantomKeys = redisTemplate.keys(Constantes.REDIS_SESSION_PREFIX + "*:phantom");
            if (phantomKeys != null && !phantomKeys.isEmpty()) {
                phantomKeys.forEach(phantomKey -> {
                    try {
                        String baseKey = phantomKey.replace(":phantom", "");
                        // Verificar si la sesión principal existe y no está expirada
                        Long ttl = redisTemplate.getExpire(baseKey);
                        if (ttl == null || ttl == -2) { // Si no existe o está expirada
                            RedisUtils.safeDelete(redisTemplate, phantomKey);
                            log.debug("Deleted orphaned phantom key: {}", phantomKey);
                        }
                    } catch (Exception e) {
                        log.error("Error processing phantom key {}: {}", phantomKey, e.getMessage());
                    }
                });
            }
        } catch (Exception e) {
            log.error("Error cleaning phantom keys: {}", e.getMessage());
        }
    }

    private void enforceKeyExpiration(Set<String> keys) {
        keys.forEach(key -> {
            try {
                Long ttl = redisTemplate.getExpire(key);
                if (ttl != null && ttl == -1) {
                    RedisUtils.safeExpire(redisTemplate, key, 
                        Constantes.REDIS_TTL_SECONDS, TimeUnit.SECONDS);
                    log.debug("Set expiration for key: {}", key);
                }
            } catch (Exception e) {
                log.error("Error setting expiration for key {}: {}", key, e.getMessage());
            }
        });
    }
}