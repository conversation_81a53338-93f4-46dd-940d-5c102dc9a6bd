package ar.gob.lapampa.nsl.seguridad.controllers;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootVersion;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import ar.gob.lapampa.nsl.seguridad.services.RedisCleanupService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Main Application Controller Defines default check end points
 * 
 * @version 1.0.0
 */
@RestController
@RequestMapping
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class AplicacionController {

    /**
     * Application name from bootstrap.yml
     */
    @Value("${application}")
    private String application;

    /**
     * Version from pom.xml
     */
    @Value("${project.version}")
    private String version;

    /**
     * Database username from bootstrap.yml
     */
    @Value("${spring.datasource.username}")
    private String dbUser;

    /**
     * Active Spring profile
     * @see org.springframework.core.env.Environment#getActiveProfiles()
     */
    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * Hikari connection pool name
     */
    @Value("${spring.datasource.hikari.pool-name}")
    private String poolName;

    /**
     * Configuration origin identifier
     */
    @Value("${config-origin}")
    private String configOrigin;

    /**
     * Runtime environment from bootstrap.yml
     */
    @Value("${environment}")
    private String runtimeEnv;

    @Autowired
    private RedisCleanupService redisCleanupService;

  /**
   * Check de estado
   * 
   * @return String
   */
  @Operation(summary = "Estado del servicio", description = "Devuelve estado del servicio",
      tags = {"Status"}, hidden = false, deprecated = false)
  @GetMapping("/estado")
  public ResponseEntity<String> healthCheck() {
    final StringBuilder msj = new StringBuilder(255);
    final String javaVersion = System.getProperty("java.version");
    final String springVersion = SpringBootVersion.getVersion();
    msj.append(application).append(": Ok\nProject version: ").append(version).append("\nJava version: ")
        .append(javaVersion).append("\nProfile activo: ").append(activeProfile)
        .append("\nPool Name: ").append(poolName).append("\nConfigs desde: ").append(configOrigin)
        .append("\nDB User: ").append(dbUser).append("\nSpring Boot version: ")
        .append(springVersion).append("\nRuntime Env.: ").append(runtimeEnv);
    return ResponseEntity.ok(msj.toString());
  }

  /**
   * Check logging levels
   * 
   * @return String
   */
  @Operation(summary = "Check de logs con lombok",
      description = "Dispara un log de cada tipo para verificar", tags = {"Status"}, hidden = false,
      deprecated = false)
  @GetMapping("/logsTest")
  public String logsCheck() {
    log.trace("Logs check: TRACE Message");
    log.debug("Logs check: DEBUG Message");
    log.info("Logs check: INFO Message");
    log.warn("Logs check: WARN Message");
    log.error("Logs check: ERROR Message");

    return "Ok! Check salida de Logs...";
  }

  @Operation(
      summary = "Forzar limpieza de Redis", 
      description = "Ejecuta la limpieza programada de Redis manualmente",
      tags = {"Status"}, 
      hidden = false, 
      deprecated = false
  )
  @GetMapping("/redis/cleanup")
  public ResponseEntity<String> forceRedisCleanup() {
    try {
      redisCleanupService.enforceExpiration();
      return ResponseEntity.ok("Limpieza de Redis ejecutada correctamente");
    } catch (Exception e) {
      log.error("Error durante la limpieza de Redis: {}", e.getMessage());
      return ResponseEntity.internalServerError()
          .body("Error durante la limpieza de Redis: " + e.getMessage());
    }
  }
}

