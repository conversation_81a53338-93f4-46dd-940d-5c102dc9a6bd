package ar.gob.lapampa.nsl.seguridad.models;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.seguridad.dtos.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UsuarioLogModel extends BaseRRHHDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 6825110916191992407L;
  private String login;
  private String tipo;
  private String estado;
  private Date creado;
  private String resultado;

  public UsuarioLogModel(String login) {
    super();
    this.login = login;
  }

  @QueryProjection
  public UsuarioLogModel(Long id) {
    super();
    this.id = id;
  }

}
