package ar.gob.lapampa.nsl.seguridad.services;

import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.seguridad.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhUsuarioLogDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.UsuarioLogRequestDTO;


@Service
public interface RrhhUsuarioLogService {

  GenericResponseDTO listar(UsuarioLogRequestDTO request);

  GenericResponseDTO registrar(RrhhUsuarioLogDTO rrhhUsuariosDTO);

  GenericResponseDTO modificar(RrhhUsuarioLogDTO rrhhUsuariosDTO, long id);

  GenericResponseDTO eliminar(Long id);

}
