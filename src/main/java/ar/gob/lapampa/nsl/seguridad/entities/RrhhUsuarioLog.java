package ar.gob.lapampa.nsl.seguridad.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "RRHH_USUARIOS_LOGS")
@SequenceGenerator(name = "RRHH_USR_LOGS_SEQ", sequenceName = "RRHH_USR_LOGS_SEQ",
    allocationSize = 1)
public class RrhhUsuarioLog implements Serializable {

  @Serial
  private static final long serialVersionUID = -8877515926033801846L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_USR_LOGS_SEQ")
  private Long id;

  @Basic(optional = false)
  @Size(min = 1, max = 20)
  @Column(name = "LOGIN")
  private String login;

  @Basic(optional = false)
  @Size(min = 1, max = 50)
  @Column(name = "TIPO")
  private String tipo;

  @Basic(optional = false)
  @Size(min = 1, max = 20)
  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "CREADO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date creado;

  @Basic(optional = false)
  @Column(name = "RESULTADO")
  private String resultado;

  public RrhhUsuarioLog(@Nonnull Long id) {
    this.id = id;
  }

}
