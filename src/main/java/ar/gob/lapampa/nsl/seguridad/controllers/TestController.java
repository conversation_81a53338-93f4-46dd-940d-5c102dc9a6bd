package ar.gob.lapampa.nsl.seguridad.controllers;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/test")
public class TestController {
  @GetMapping("/all")
  public String allAccess() {
    return "Contenido de acceso publico.";
  }

  @GetMapping("/user")
  @PreAuthorize("hasRole('ROLE_CONSULTOR') or hasRole('ROLE_ADMINISTRADOR')")
  public String userAccess() {
    return "Lugar exclusivo para roles consultor y/o administrador.";
  }

  @GetMapping("/pre")
  @PreAuthorize("hasRole('ROLE_PRESUPUESTO') and hasAuthority('CATEGORIAS_REGISTRAR')")
  public String moderatorAccess() {
    return "Lugar exclusivo para rol presupuesto.";
  }

  @GetMapping("/admin")
  @PreAuthorize("hasRole('ROLE_ADMINISTRADOR')")
  public String adminAccess() {
    return "Lugar exclusivo para rol administrador.";
  }

  @GetMapping("/auth")
  @PreAuthorize("hasRole('ROLE_ADMINISTRADOR')")
  public Map<String, Object> getPrincipalInfo() {
    Authentication principal = SecurityContextHolder.getContext().getAuthentication();

    Collection<String> authorities =
        principal.getAuthorities().stream().map(GrantedAuthority::getAuthority).toList();

    Map<String, Object> info = new HashMap<>();
    info.put("name: ", principal.getName());
    info.put("authorities: ", authorities);
    info.put("isAuthenticated: ", principal.isAuthenticated());

    return info;
  }
}
