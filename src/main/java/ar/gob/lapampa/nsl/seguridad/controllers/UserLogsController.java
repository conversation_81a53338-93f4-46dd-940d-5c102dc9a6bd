package ar.gob.lapampa.nsl.seguridad.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.seguridad.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhUsuarioLogDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.UsuarioLogRequestDTO;
import ar.gob.lapampa.nsl.seguridad.services.RrhhUsuarioLogService;
import jakarta.validation.Valid;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@NoArgsConstructor
@Slf4j
@RequestMapping("/api/v2/userLogs")
public class UserLogsController {

  @Autowired
  RrhhUsuarioLogService rrhhUsuarioLogService;

  /**
   * Persiste logs del usuario (validacion de permisos y roles)
   *
   * @return GenericResponseDTO
   * 
   *
   */
  @PostMapping("/crear")
  public ResponseEntity<?> createUserLogs(@Valid @RequestBody RrhhUsuarioLogDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    try {
      if (request == null || request.getLogin() == null) {
        response.setEstadoError("Login de usuario no puede ser null");
        return ResponseEntity.badRequest().body(response);
      }
      log.info("Guardando logs de usuario...");
      response = rrhhUsuarioLogService.registrar(request);
      return ResponseEntity.ok(response);
    } catch (Exception e) {
      log.error("Error guardando logs de usuario: {}", e.getMessage());
      response.setEstadoError("Error al guardar logs de usuario: %s".formatted(e.getMessage()));
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

  /**
   * Persiste logs del usuario (validacion de permisos y roles)
   *
   * @return GenericResponseDTO
   * 
   *
   */
  @PostMapping("/listar")
  public ResponseEntity<?> listarUserLogs(@Valid @RequestBody UsuarioLogRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    try {
      if (request == null || request.getLogin() == null) {
        response.setEstadoError("Login de usuario no puede ser null");
        return ResponseEntity.badRequest().body(response);
      }
      log.info("Listando logs de usuarios...");
      response = rrhhUsuarioLogService.listar(request);
      return ResponseEntity.ok(response);
    } catch (Exception e) {
      log.error("Error liatando logs de usuarios: {}", e.getMessage());
      response.setEstadoError("Error al listar logs de usuarios: %s".formatted(e.getMessage()));
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

}
