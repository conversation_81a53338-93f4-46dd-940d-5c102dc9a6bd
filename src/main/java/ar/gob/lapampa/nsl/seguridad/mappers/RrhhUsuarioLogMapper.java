package ar.gob.lapampa.nsl.seguridad.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhUsuarioLogDTO;
import ar.gob.lapampa.nsl.seguridad.entities.RrhhUsuarioLog;
import ar.gob.lapampa.nsl.seguridad.models.UsuarioLogModel;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RrhhUsuarioLogMapper {

  RrhhUsuarioLogMapper INSTANCE = Mappers.getMapper(RrhhUsuarioLogMapper.class);

  RrhhUsuarioLogDTO rrhhUsuarioLogToRrhhUsuarioLogDto(RrhhUsuarioLog userLog);

  static UsuarioLogModel rrhhUsuarioLogToRrhhUsuarioLogModel(RrhhUsuarioLog userLog) {
    UsuarioLogModel model = new UsuarioLogModel(userLog.getId());
    model.setLogin(userLog.getLogin());
    model.setTipo(userLog.getTipo());
    model.setEstado(userLog.getEstado());
    model.setResultado(userLog.getResultado());
    model.setCreado(userLog.getCreado());
    return model;
  }

}
