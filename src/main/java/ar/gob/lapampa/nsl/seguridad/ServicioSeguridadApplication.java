package ar.gob.lapampa.nsl.seguridad;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

/**
 * Microservice Seguridad
 *
 * <AUTHOR> NSL
 * @version 1.0.0
 * 
 */
@SpringBootApplication
public class ServicioSeguridadApplication {

  /**
   * Método principal
   * 
   * @param args argumentos del servicio
   */
  public static void main(String[] args) {
    SpringApplication.run(ServicioSeguridadApplication.class, args);
  }

  /**
   * Open API Definitions.
   *
   * @return open API resume
   */
  @Bean
  OpenAPI customOpenAPI(@Value("${project.description}") String appDesciption,
      @Value("${project.version}") String appVersion) {
    return new OpenAPI().info(new Info().title("Servidor API para Gestionar Seguridad")
        .version(appVersion).description(appDesciption).termsOfService("http://swagger.io/terms/")
        .license(new License().name("Apache 2.0").url("http://springdoc.org")));
  }

}
