package ar.gob.lapampa.nsl.seguridad.controllers;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import ar.gob.lapampa.nsl.seguridad.dtos.*;
import ar.gob.lapampa.nsl.seguridad.entities.RrhhUsuario;
import ar.gob.lapampa.nsl.seguridad.jwt.JwtUtils;
import ar.gob.lapampa.nsl.seguridad.mappers.RrhhUsuarioMapper;
import ar.gob.lapampa.nsl.seguridad.repositories.RrhhUsuarioRepository;

import org.apache.commons.lang.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;
import ar.gob.lapampa.nsl.seguridad.entities.UserSession;
import ar.gob.lapampa.nsl.seguridad.repositories.UserSessionRepository;
import ar.gob.lapampa.nsl.seguridad.services.GamUserService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/oauth/gam")
public class GamController {

	private static final String REDIRECT = "redirect:";

	@Autowired
	private GamUserService gamUserService;

	@Autowired
	private UserSessionRepository userSessionRepository;

	@Autowired
	private JwtUtils jwtUtils;

	@Autowired
	private RrhhUsuarioRepository rrhhUsuarioRepository;

	@Operation(summary = "Redirige a la url de login", description = "Redirige a la url de login", tags = {
			"login" }, hidden = false, deprecated = false)
	@GetMapping("/login")
	public ModelAndView login() {

		return new ModelAndView(REDIRECT + gamUserService.getLoginUrl());
	}

	@Operation(summary = "Bff para manejo de respuesta de redirect", description = "Obtiene el token y redirige con la info obtenida", tags = {
			"callback" })
	@GetMapping("/callback")
	public ModelAndView callback(@RequestParam String gam, @RequestParam String code) {
		GenericResponseDTO dto = gamUserService.validar(new GamCredentialsDTO(gam, code));

		if (Objects.nonNull(dto)) {
			TokenResponseDTO token = (TokenResponseDTO) dto.getObjeto();

			if (Objects.nonNull(token)) {
				return new ModelAndView(
						"%s%s?token=%s&refresh=%s&expire=%s".formatted(REDIRECT, gamUserService.getWelcomeUrl(),
								token.getAccess_token(), token.getRefresh_token(), token.getExpires_in().toString()));
			}
		}
		return new ModelAndView(REDIRECT + gamUserService.getLoginUrl());
	}

	@Operation(summary = "Obtiene la info de un usuario por token", description = "Obtiene la info de un usuario por token", tags = {
			"userinfo" }, hidden = false, deprecated = false)
	@GetMapping("/userinfo")
	public GenericResponseDTO userinfo(@RequestParam String token) {
		GenericResponseDTO dto = new GenericResponseDTO();
		GamUserInfoDTO user = null;
		UserSession userSession = null;

		if (token == null || token.trim().isEmpty()) {
			dto.setEstadoError("Token no puede ser vacío");
			return dto;
		}

		try {
			if (token.contains("!")) {
				// Buscar por token externo (GAM)
				List<UserSession> sessions = userSessionRepository.findByExternalToken(token);
				if (sessions.isEmpty()) {
					dto.setEstadoError("Sesión no encontrada para el token proporcionado");
					return dto;
				}
				userSession = sessions.getFirst();
				user = gamUserService.getUserInfo(token);
			} else {
				// Buscar por token JWT
				List<UserSession> sessions = userSessionRepository.findByToken(token);
				if (sessions.isEmpty()) {
					dto.setEstadoError("Sesión no encontrada para el token proporcionado");
					return dto;
				}
				userSession = sessions.getFirst();
				try {
					user = gamUserService.getUserInfo(userSession.getExternalToken());
				} catch (Exception e) {
					dto.setEstadoError("Error al obtener información del usuario: %s".formatted(e.getMessage()));
					return dto;
				}
			}

			if (user == null || user.getUsuario() == null) {
				dto.setEstadoError("Token inválido o expirado");
				return dto;
			}

			user.setNombreCompleto(WordUtils.capitalizeFully(userSession.getName()));
			dto.setEstadoExito(user);

			if (userSession.isActive()) {
				dto.setMensaje(Long.toString(userSession.getRemainigMillis()));
			} else {
				dto.setEstadoError("Sesión expirada");
				return dto;
			}

			return dto;
		} catch (Exception e) {
			dto.setEstadoError("Error al procesar la solicitud: " + e.getMessage());
			return dto;
		}
	}

	@Operation(summary = "Obtiene el token en base al refresh token", description = "Obtiene el token en base al refresh token", tags = {
			"refresh" }, hidden = false, deprecated = false)
	@GetMapping("/refresh")
	public GenericResponseDTO refresh(@RequestParam(name = "refresh_token") String refreshToken) {
		GenericResponseDTO dto = new GenericResponseDTO();
		if (Objects.nonNull(refreshToken) && (!refreshToken.isEmpty())) {
			if (refreshToken.contains(".")) {
				// Refresh JWT
				String username = jwtUtils.getUserNameFromJwtToken(refreshToken);
				if (username != null) {
					// Buscar sesión por refresh token JWT
					List<UserSession> sessions = userSessionRepository.findByRefresh(refreshToken);
					if (!sessions.isEmpty()) {
						UserSession session = sessions.getFirst();
						if (session.isActive()) {
							// Obtener token GAM usando el refresh token externo
							TokenResponseDTO gamToken = gamUserService.getTokenByRefresh(session.getExternalRefresh());
							if (gamToken != null) {
								// Generar nuevos tokens JWT
								RrhhUsuario rrhhUser = rrhhUsuarioRepository.findByLogin(username);
								RrhhUsuarioDTO userDto = RrhhUsuarioMapper.INSTANCE
										.rrhhUsuarioToRrhhUsuarioDto(rrhhUser);

								// Generar nuevos tokens JWT
								String newJwtToken = jwtUtils.generateJwtToken(userDto);
								String newRefreshToken = jwtUtils.generateRefreshToken(userDto);

								// Actualizar sesión en Redis
								session.setToken(newJwtToken);
								session.setRefresh(newRefreshToken);
								session.setExternalToken(gamToken.getAccess_token());
								session.setExternalRefresh(gamToken.getRefresh_token());
								session.setCallbackDate(new Date().getTime());
								userSessionRepository.save(session);

								// Crear respuesta con nuevos tokens
								TokenResponseDTO response = new TokenResponseDTO();
								response.setAccess_token(newJwtToken);
								response.setRefresh_token(newRefreshToken);
								response.setExpires_in(jwtUtils.getJwtExpirationMs() / 1000);
								response.setToken_type("Bearer");

								dto.setEstadoExito(response);
								return dto;
							}
						} else {
							dto.setEstadoError("Sesión expirada");
							return dto;
						}
					}
				}
			} else {
				// Refrescamos solo GAM
				TokenResponseDTO token = gamUserService.getTokenByRefresh(refreshToken);
				if (Objects.nonNull(token)) {
					dto.setEstadoExito(token);
					return dto;
				}
			}
		}
		dto.setEstadoError("Token inválido");
		return dto;
	}

	@Operation(summary = "Bff Termina la sesión del token", description = "Termina la sesión del token y redirige", tags = {
			"signout" })
	@GetMapping("/signout")
	public ModelAndView signout(@RequestParam String token, @RequestParam String sessionId) {
		// Delete of Redis session
		if (gamUserService.signoutUserBySessionId(Long.parseLong(sessionId))) {
			return new ModelAndView(REDIRECT + gamUserService.getLogoutUrl(token));
		} else {
			return new ModelAndView(REDIRECT + gamUserService.getLoginUrl());
		}
	}

	@Operation(summary = "Termina la sesión del token y borra Redis session", description = "Termina la sesión del token y devuelve json", tags = {
			"signoutJson" }, hidden = false, deprecated = false)
	@GetMapping("/signoutJson")
	public GenericResponseDTO signoutJson(@RequestParam String token) {
		GenericResponseDTO dto = new GenericResponseDTO();
		
		if (esJWT(token)) {
			UserSession userSession = userSessionRepository.findByToken(token).getFirst();
			token = userSession.getExternalToken();
		}
		// Delete of Redis session
		if (gamUserService.signoutUserByToken(token)) {
			String logoutUrl = gamUserService.getLogoutUrl(token);
			dto.setEstadoExito(logoutUrl);
		} else {
			dto.setEstadoError("Token y / o sessionId inválidos");
		}
		return dto;
	}

	@Operation(summary = "Valida vigencia del token en REDIS / GAM", description = "Corrobora contra redis y luego gam la sesión del token y devuelve boolean", tags = {
			"validarToken" })
	@PostMapping("/validarToken")
	public Boolean validaToken(@Valid @RequestBody JwtRequestDTO request) {
		String token = request.getToken();
		List<UserSession> sessionList;

		if (token.contains("!")) {
			// Search in Redis by GAM Token
			sessionList = userSessionRepository.findByExternalToken(token);
		} else {
			// Search in Redis by JWT Token
			sessionList = userSessionRepository.findByToken(token);
		}
		if (!sessionList.isEmpty()) {
			// found in Redis then query GAM with external Token (optional)
			String externalToken = sessionList.getFirst().getExternalToken();
			GamUserInfoDTO userinfo = gamUserService.getUserInfo(externalToken);
			return userinfo.getUsuario() != null;
		} else {
			return false;
		}
	}
	
	private boolean esJWT(String token) {
		return token != null && token.chars().filter(ch -> ch == '.').count() == 2;
	}
}
