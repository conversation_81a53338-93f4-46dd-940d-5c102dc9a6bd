package ar.gob.lapampa.nsl.seguridad.services;

import jakarta.validation.Valid;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.seguridad.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RegistrarEscalafonesAUsuarioRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RegistrarRolesAUsuarioRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhUsuarioDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.TokenRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.TokenResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.UserCredentialsDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.UsuarioRequestDTO;

@Transactional
public interface RrhhUsuarioService {

  GenericResponseDTO listar(UsuarioRequestDTO request);

  GenericResponseDTO registrar(UserCredentialsDTO signUpRequest);

  GenericResponseDTO modificar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO eliminar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO deshabilitar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO habilitar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO registrarRolesAUsuario(RegistrarRolesAUsuarioRequestDTO request);

  GenericResponseDTO registrarEscalafonesAUsuario(RegistrarEscalafonesAUsuarioRequestDTO request);

  GenericResponseDTO findByLogin(UserCredentialsDTO request);

  GenericResponseDTO login(UserCredentialsDTO request);

  GenericResponseDTO rolesByLogin(String login);

  GenericResponseDTO authenticate(@Valid UserCredentialsDTO request);

  UsernamePasswordAuthenticationToken validate(Authentication auth);

  void createSession(TokenResponseDTO data, String username);

  GenericResponseDTO menusByToken(TokenRequestDTO request, String login);
}
