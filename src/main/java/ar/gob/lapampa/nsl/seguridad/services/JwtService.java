package ar.gob.lapampa.nsl.seguridad.services;

import java.util.List;
import org.springframework.security.core.Authentication;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhRolDTO;

public interface JwtService {

  boolean validateToken(String token);

  String generateToken(Authentication authentication);

  String getMockToken(String login, List<RrhhRolDTO> roles);

  String getUserNameFromJwtToken(String token);

}
