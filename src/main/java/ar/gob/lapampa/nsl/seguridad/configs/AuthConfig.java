package ar.gob.lapampa.nsl.seguridad.configs;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import ar.gob.lapampa.nsl.seguridad.jwt.AuthEntryPointJwt;
import ar.gob.lapampa.nsl.seguridad.jwt.AuthTokenFilter;
import ar.gob.lapampa.nsl.seguridad.services.RedisUserDetailsService;

@Configuration
@EnableMethodSecurity
// (securedEnabled = true,
// jsr250Enabled = true,
// prePostEnabled = true) // by default
public class AuthConfig { // extends WebSecurityConfigurerAdapter {

  @Autowired
  private AuthEntryPointJwt unauthorizedHandler;

  @Bean
  AuthTokenFilter authenticationJwtTokenFilter() {
    return new AuthTokenFilter();
  }

  @Bean
  RedisUserDetailsService redisUserDetailsService() {
    return new RedisUserDetailsService();
  }

  @Bean
  DaoAuthenticationProvider authenticationProvider() {
    DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();

    authProvider.setUserDetailsService(redisUserDetailsService());
    authProvider.setPasswordEncoder(passwordEncoder());

    return authProvider;
  }


  @Bean
  AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig)
      throws Exception {
    return authConfig.getAuthenticationManager();
  }

  @Bean
  PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
  }

  @Bean
  SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    http.csrf(AbstractHttpConfigurer::disable)
        .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
        .sessionManagement(
            session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .authorizeHttpRequests(auth -> auth.requestMatchers(
            // Public end points
            AntPathRequestMatcher.antMatcher("/api/v2/app/credenciales/**"),
            AntPathRequestMatcher.antMatcher("/api/v2/userLogs/**"),
            AntPathRequestMatcher.antMatcher("/oauth/gam/**"),
            AntPathRequestMatcher.antMatcher("/estado"),
            AntPathRequestMatcher.antMatcher("/redis/cleanup/**"),
            AntPathRequestMatcher.antMatcher("/logsTest"),
            AntPathRequestMatcher.antMatcher("/api/v1/app/credenciales/**"),
            AntPathRequestMatcher.antMatcher("/v3/api-docs/**"),
            AntPathRequestMatcher.antMatcher("/swagger-ui/**"),
            AntPathRequestMatcher.antMatcher("/actuator/**"),
            AntPathRequestMatcher.antMatcher("/error")).permitAll()
            .requestMatchers(AntPathRequestMatcher.antMatcher("/api/test/**")).permitAll()
            .anyRequest().authenticated());

    http.authenticationProvider(authenticationProvider());

    http.addFilterBefore(authenticationJwtTokenFilter(),
        UsernamePasswordAuthenticationFilter.class);

    return http.build();
  }
}
