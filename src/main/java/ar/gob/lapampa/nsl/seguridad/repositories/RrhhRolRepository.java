package ar.gob.lapampa.nsl.seguridad.repositories;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.seguridad.entities.RrhhRol;

@Repository
public interface RrhhRolRepository
    extends JpaRepository<RrhhRol, Long>, QuerydslPredicateExecutor<RrhhRol> {

  Optional<RrhhRol> findByNombre(String nombre);

}
