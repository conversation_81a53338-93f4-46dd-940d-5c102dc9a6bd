package ar.gob.lapampa.nsl.seguridad.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.seguridad.entities.RrhhUsuario;

@Repository
public interface RrhhUsuarioRepository
    extends JpaRepository<RrhhUsuario, Long>, QuerydslPredicateExecutor<RrhhUsuario> {

  RrhhUsuario findByLogin(String login);

  Boolean existsByLogin(String login);


}
