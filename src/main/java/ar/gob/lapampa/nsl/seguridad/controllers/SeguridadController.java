package ar.gob.lapampa.nsl.seguridad.controllers;

import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.seguridad.client.WsUserClient;
import ar.gob.lapampa.nsl.seguridad.client.gen.WSValidarUsuarioExecuteResponse;
import ar.gob.lapampa.nsl.seguridad.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.JwtRequestDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.RrhhRolDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.UserCredentialsDTO;
import ar.gob.lapampa.nsl.seguridad.dtos.ValidationResponseDTO;
import ar.gob.lapampa.nsl.seguridad.services.JwtService;
import ar.gob.lapampa.nsl.seguridad.services.RedisUserDetailsService;
import ar.gob.lapampa.nsl.seguridad.services.RrhhUsuarioService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@NoArgsConstructor
@Slf4j
@RequestMapping("/api/v1/app/credenciales")
public class SeguridadController {

  @Autowired
  private WsUserClient wsUserClient;

  @Autowired
  private RrhhUsuarioService rrhhUsuarioService;

  @Autowired
  private JwtService jwtService;

  @Autowired
  RedisUserDetailsService userDetailsService;

  /**
   * Valída credenciales de usuario único
   * 
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController)
   *
   */
  @Operation(summary = "Validar Credenciales S.U.U.",
      description = "Válida credenciales del usuario único", tags = {"Validar"})
  @PostMapping("/validarSuu")
  @Deprecated
  public ResponseEntity<ValidationResponseDTO> validarCredencialesSuu(
      @RequestBody UserCredentialsDTO credenciales) {
    WSValidarUsuarioExecuteResponse response = wsUserClient.validar(credenciales);
    return ResponseEntity
        .ok(new ValidationResponseDTO(response.getRespuesta(), response.getDescripcion()));
  }

  /**
   * Valída credenciales de usuario único
   * 
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController)
   *
   */
  @Operation(summary = "Validar Credenciales S.U.U.",
      description = "Válida credenciales del usuario único", tags = {"Validar"})
  @PostMapping("/validar")
  @Deprecated
  public ResponseEntity<UsernamePasswordAuthenticationToken> validarCredenciales(
      @Valid @RequestBody Authentication auth) {
    UsernamePasswordAuthenticationToken response = rrhhUsuarioService.validate(auth);
    return ResponseEntity.ok(response);
  }

  /**
   * Valida credenciales de usuario
   * 
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController).
   *
   */
  @Operation(summary = "Validar Credenciales", description = "Válida credenciales del usuario",
      tags = {"Validar"})
  @Transactional
  @PostMapping("/findByLogin")
  @Deprecated
  public @ResponseBody GenericResponseDTO findByLogin(
      @Valid @RequestBody UserCredentialsDTO request) {

    return rrhhUsuarioService.findByLogin(request);
  }

  /**
   * Valida credenciales de usuario
   * 
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController)
   *
   */
  @Operation(summary = "Obtiene Roles del Usuario por su Login",
      description = "Válida credenciales del usuario", tags = {"Validar"})
  @Transactional
  @GetMapping("/rolesByLogin")
  @Deprecated
  public @ResponseBody GenericResponseDTO rolesByLogin(@Valid @RequestParam String login) {

    return rrhhUsuarioService.rolesByLogin(login);
  }

  /**
   * Genera token JWT de usuario
   * 
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController)
   *
   */
  @Operation(summary = "Genera Token JWT", description = "Genera Token con expiración",
      tags = {"Token"})
  @Transactional
  @PostMapping("/token")
  public @ResponseBody GenericResponseDTO getToken(@Valid @RequestBody UserCredentialsDTO request) {

    GenericResponseDTO result = new GenericResponseDTO();
    ArrayList<RrhhRolDTO> roles = new ArrayList<>();
    roles.add(new RrhhRolDTO(1L)); // as admin
    String token = jwtService.getMockToken(request.getLogin(), roles);
    result.setEstadoExito(token);
    return result;

  }

  /**
   * Valida credenciales de usuario
   * 
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController).
   *
   */
  @Operation(summary = "Validar Credenciales", description = "Válida credenciales del usuario",
      tags = {"Validar"}, deprecated = true)
  @Transactional
  @PostMapping("/login")
  @ResponseBody
  @Deprecated
  public GenericResponseDTO login(@Valid @RequestBody UserCredentialsDTO request) {

    log.info("Ingreso a login... ");
    return rrhhUsuarioService.login(request);
  }

  /**
   * Valida credenciales de usuario
   *
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController).
   *
   */
  @Transactional
  @PostMapping("/signin")
  @Deprecated
  public GenericResponseDTO authenticateUser(@Valid @RequestBody UserCredentialsDTO request) {

    log.info("Ingreso a signin... ");
    return rrhhUsuarioService.authenticate(request);
  }

  /**
   * Valida credenciales de usuario
   *
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController).
   *
   */
  @PostMapping("/signup")
  @Deprecated
  public GenericResponseDTO registerUser(@Valid @RequestBody UserCredentialsDTO signUpRequest) {

    log.info("Ingreso a signUp... ");
    return rrhhUsuarioService.registrar(signUpRequest);

  }

  /**
   * Valida token de usuario
   *
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController).
   *
   */
  @PostMapping("/validarToken")
  @Deprecated
  public ResponseEntity<?> validaToken(@Valid @RequestBody JwtRequestDTO request) {
    try {
      if (request == null || request.getToken() == null) {
        GenericResponseDTO response = new GenericResponseDTO();
        response.setEstadoError("Token no puede ser null");
        return ResponseEntity.badRequest().body(response);
      }

      log.info("Validando token...");
      if (jwtService.validateToken(request.getToken())) {
        String username = jwtService.getUserNameFromJwtToken(request.getToken());
        GenericResponseDTO response = new GenericResponseDTO();
        response.setEstadoExito(username);
        return ResponseEntity.ok(response);
      }

      GenericResponseDTO response = new GenericResponseDTO();
      response.setEstadoError("Token inválido");
      return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);

    } catch (Exception e) {
      log.error("Error validando token: {}", e.getMessage());
      GenericResponseDTO response = new GenericResponseDTO();
      response.setEstadoError("Error al validar token: %s".formatted(e.getMessage()));
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

  /**
   * Valida credenciales de usuario
   *
   * @return String
   *
   * @deprecated El uso de este método se desaconseja. Por favor, utilice el API V2
   *             (GamSeguridadController).
   *
   */
  @PostMapping("/userDetails")
  @Deprecated
  public ResponseEntity<?> getUserDetails(@Valid @RequestBody JwtRequestDTO request) {
    try {
      if (request == null || request.getToken() == null) {
        GenericResponseDTO response = new GenericResponseDTO();
        response.setEstadoError("Token no puede ser null");
        return ResponseEntity.badRequest().body(response);
      }

      log.info("Obteniendo detalles de usuario...");
      UserDetails userDetails = userDetailsService.loadUserByUsername(request.getToken());
      GenericResponseDTO response = new GenericResponseDTO();
      response.setEstadoExito(userDetails);
      return ResponseEntity.ok(response);

    } catch (UsernameNotFoundException e) {
      GenericResponseDTO response = new GenericResponseDTO();
      response.setEstadoError("Usuario no encontrado: {}%s".formatted(e.getMessage()));
      return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    } catch (Exception e) {
      log.error("Error obteniendo detalles de usuario: {}", e.getMessage());
      GenericResponseDTO response = new GenericResponseDTO();
      response.setEstadoError("Error al obtener detalles: %s".formatted(e.getMessage()));
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

}
