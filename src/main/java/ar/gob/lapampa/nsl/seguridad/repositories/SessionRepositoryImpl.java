package ar.gob.lapampa.nsl.seguridad.repositories;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Repository
@Slf4j
public class SessionRepositoryImpl implements SessionRepository {
    
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void setExpire(String key, long seconds) {
        try {
            Boolean result = redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(result)) {
                log.warn("Failed to set expiration for key: {}", key);
            }
        } catch (Exception e) {
            log.error("Error setting expiration for key {}: {}", key, e.getMessage());
        }
    }
}
