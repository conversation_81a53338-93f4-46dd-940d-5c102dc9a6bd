application: Servicio Seguridad
config-origin: Service yml file
environment: ${RUNTIME_ENVIRONMENT:none}
eureka:
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${random.uuid}
gam:
  config:
    client_id: "8dadf0ee895f4d04886f600d642b82f1"
    client_secret_desa: "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263"
    client_secret_test: "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263"
    client_secret_prod: "e4c0590974e14999ad5aa8384810ad1e46666052d75341e3bc70459ab0b1daad0ce927bf"
    base_url_desa: "https://gxdemo5.lapampa.gob.ar/suugam"
    base_url_test: "https://suudemo.lapampa.gob.ar/suugam"
    base_url_prod: "https://suu.lapampa.gob.ar/suugam" 
    redirect_uri_desa: "https://desannslapi.lapampa.gob.ar/servicio-seguridad/oauth/gam/callback"
    redirect_uri_test: "https://testnnslapi.lapampa.gob.ar/servicio-seguridad/oauth/gam/callback"
    redirect_uri_prod: "https://nnslapi.lapampa.gob.ar/servicio-seguridad/oauth/gam/callback"
    welcome_uri_desa: "http://desannsl.lapampa.gob.ar/#/welcome"
    welcome_uri_test: "http://testnnsl.lapampa.gob.ar/#/welcome"
    welcome_uri_prod: "http://nnsl.lapampa.gob.ar/#/welcome"
    logout_uri_prod: "http://nnsl.lapampa.gob.ar/"
    logout_uri_test: "http://testnnsl.lapampa.gob.ar/"
    logout_uri_desa: "http://desannsl.lapampa.gob.ar/"
management:
  endpoint:
    configprops:
      show-values: ALWAYS
    env:
      post:
        enabled: true
      show-values: ALWAYS
    health:
      show-details: ALWAYS
    logfile: 
      enabled: true
    quartz:
      show-values: ALWAYS
    refresh:
      enabled: true
    restart:
      enabled: false
    shutdown:
      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
project:
  description: '@project.description@'
  version: '@project.version@'
server:
  port: 0
spring:
  application:
    name: servicio-seguridad
  cloud:
    compatibility-verifier:
      enabled: false
    config:
      bus:
        enabled: true
        refresh:
          enabled: true
      uri: http://localhost:8888
  data:
    redis:
      host: localhost
      port: 6379
      password: cesida#lapampa
      database: 0
      timeout: 60
  datasource:
    hikari:
      connection-test-query: SELECT 1 FROM DUAL
      connection-timeout: 20000
      idle-timeout: 60000
      leak-detection-threshold: 90000
      max-lifetime: 120000
      maximum-pool-size: 5
      minimum-idle: 2
      pool-name: HikariPool-seguridad
    password: rjqUsKi2mxwog74P4VLN
    username: LAPAMPA
  jpa:
    generate-ddl: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.OracleDialect
    show-sql: true
  profiles:
    active: ${RUNTIME_ENVIRONMENT:desa}
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    config-url: /${spring.application.name}/v3/api-docs/swagger-config
    disable-swagger-default-url: true
    doc-expansion: none
    path: /swagger-ui
    url: /${spring.application.name}/v3/api-docs
---
logging:
  level:
    root: INFO
spring:
  banner:
    location: classpath:banner_local.txt
  config:
    activate:
      on-profile: local
  datasource:
    password: rjqUsKi2mxwog74P4VLN
    url: ***********************************
    username: LAPAMPA
---
logging:
  level:
    root: ERROR
spring:
  banner:
    location: classpath:banner.txt
  config:
    activate:
      on-profile: desa
  datasource:
    url: *************************************
---
logging:
  level:
    root: INFO
spring:
  banner:
    location: classpath:banner_test.txt
  config:
    activate:
      on-profile: test
  datasource:
    password: Cesida#2023
    url: *************************************
    username: LP_ADMIN
---
logging:
  level:
    root: ERROR
spring:
  banner:
    location: classpath:banner_prod.txt
  config:
    activate:
      on-profile: prod
  datasource:
    password: Cesida#2023
    url: *****************************************
    username: LP_ADMIN
