<?xml version="1.0" encoding="UTF-8"?>
<definitions name="WSValidarUsuario" targetNamespace="Usuarios" xmlns:wsdlns="Usuarios" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="Usuarios">
  <types>
    <schema targetNamespace="Usuarios" elementFormDefault="qualified" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/">
      <element name="WSValidarUsuario.Execute">
        <complexType>
          <sequence>
            <element minOccurs="1" maxOccurs="1" name="Sistemacodigointerno" type="xsd:long"/>
            <element minOccurs="1" maxOccurs="1" name="Usuario" type="xsd:string"/>
            <element minOccurs="1" maxOccurs="1" name="Contrasenia" type="xsd:string"/>
          </sequence>
        </complexType>
      </element>
      <element name="WSValidarUsuario.ExecuteResponse">
        <complexType>
          <sequence>
            <element minOccurs="1" maxOccurs="1" name="Respuesta" type="xsd:short"/>
            <element minOccurs="1" maxOccurs="1" name="Descripcion" type="xsd:string"/>
          </sequence>
        </complexType>
      </element>
    </schema>
  </types>
  <message name="WSValidarUsuario.ExecuteSoapIn">
    <part name="parameters" element="tns:WSValidarUsuario.Execute"/>
  </message>
  <message name="WSValidarUsuario.ExecuteSoapOut">
    <part name="parameters" element="tns:WSValidarUsuario.ExecuteResponse"/>
  </message>
  <portType name="WSValidarUsuarioSoapPort">
    <operation name="Execute">
      <input message="wsdlns:WSValidarUsuario.ExecuteSoapIn"/>
      <output message="wsdlns:WSValidarUsuario.ExecuteSoapOut"/>
    </operation>
  </portType>
  <binding name="WSValidarUsuarioSoapBinding" type="wsdlns:WSValidarUsuarioSoapPort">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="Execute">
      <soap:operation soapAction="Usuariosaction/AWSVALIDARUSUARIO.Execute"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="WSValidarUsuario">
    <port name="WSValidarUsuarioSoapPort" binding="wsdlns:WSValidarUsuarioSoapBinding">
      <soap:address location="https://suun.lapampa.gob.ar/suu/servlet/com.suu.awsvalidarusuario"/>
    </port>
  </service>
</definitions>