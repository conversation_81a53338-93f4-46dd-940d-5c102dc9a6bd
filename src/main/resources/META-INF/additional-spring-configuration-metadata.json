{"properties": [{"name": "servicio.application.property", "type": "java.lang.String", "description": "A description for 'servicio.application.property'"}, {"name": "project.version", "type": "java.lang.String", "description": "A description for 'project.version'"}, {"name": "gam.config.client_id", "type": "java.lang.String", "description": "A description for 'gam.config.client_id'"}, {"name": "gam.config.client_secret_test", "type": "java.lang.String", "description": "A description for 'gam.config.client_secret_test'"}, {"name": "gam.config.client_secret_desa", "type": "java.lang.String", "description": "A description for 'gam.config.client_secret_desa'"}, {"name": "gam.config.client_secret_prod", "type": "java.lang.String", "description": "A description for 'gam.config.client_secret_prod'"}, {"name": "gam.config.base_url_desa", "type": "java.lang.String", "description": "A description for 'gam.config.base_url_desa'"}, {"name": "gam.config.base_url_test", "type": "java.lang.String", "description": "A description for 'gam.config.base_url_test'"}, {"name": "gam.config.base_url_prod", "type": "java.lang.String", "description": "A description for 'gam.config.base_url_prod'"}, {"name": "gam.config.redirect_uri_desa", "type": "java.lang.String", "description": "A description for 'gam.config.redirect_uri_desa'"}, {"name": "gam.config.redirect_uri_test", "type": "java.lang.String", "description": "A description for 'gam.config.redirect_uri_test'"}, {"name": "gam.config.redirect_uri_prod", "type": "java.lang.String", "description": "A description for 'gam.config.redirect_uri_prod'"}, {"name": "gam.config.welcome_uri_desa", "type": "java.lang.String", "description": "A description for 'gam.config.welcome_uri_desa'"}, {"name": "gam.config.welcome_uri_test", "type": "java.lang.String", "description": "A description for 'gam.config.welcome_uri_test'"}, {"name": "gam.config.welcome_uri_prod", "type": "java.lang.String", "description": "A description for 'gam.config.welcome_uri_prod'"}, {"name": "gam.config.logout_uri_prod", "type": "java.lang.String", "description": "A description for 'gam.config.logout_uri_prod'"}, {"name": "gam.config.logout_uri_test", "type": "java.lang.String", "description": "A description for 'gam.config.logout_uri_test'"}, {"name": "gam.config.logout_uri_desa", "type": "java.lang.String", "description": "A description for 'gam.config.logout_uri_desa'"}]}