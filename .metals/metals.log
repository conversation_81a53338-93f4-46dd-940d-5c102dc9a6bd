2023.05.19 08:04:22 INFO  Started: Metals version 0.11.12 in workspace 'E:\LaPampa\_repos\nsl-servicio-seguridad' for client Visual Studio Code 1.77.3.
may. 19, 2023 8:04:22 A.�M. org.flywaydb.core.internal.license.VersionPrinter printVersionOnly
INFO: Flyway Community Edition 9.16.3 by Redgate
may. 19, 2023 8:04:22 A.�M. org.flywaydb.core.internal.license.VersionPrinter printVersion
INFO: See release notes here: https://rd.gt/416ObMi
may. 19, 2023 8:04:22 A.�M. org.flywaydb.core.internal.license.VersionPrinter printVersion
INFO: 
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.database.base.BaseDatabaseType createDatabase
INFO: Database: jdbc:h2:file:E:\LaPampa\_repos\nsl-servicio-seguridad\.metals\metals (H2 2.1)
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory allAppliedMigrations
INFO: Schema history table "PUBLIC"."flyway_schema_history" does not exist yet
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.command.DbValidate validate
INFO: Successfully validated 4 migrations (execution time 00:00.010s)
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory create
INFO: Creating Schema History table "PUBLIC"."flyway_schema_history" ...
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.command.DbMigrate migrateGroup
INFO: Current version of schema "PUBLIC": << Empty Schema >>
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
INFO: Migrating schema "PUBLIC" to version "1 - Create tables"
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
INFO: Migrating schema "PUBLIC" to version "2 - Server discovery"
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
INFO: Migrating schema "PUBLIC" to version "3 - Jar symbols"
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
INFO: Migrating schema "PUBLIC" to version "4 - Fingerprints"
may. 19, 2023 8:04:23 A.�M. org.flywaydb.core.internal.command.DbMigrate logSummary
INFO: Successfully applied 4 migrations to schema "PUBLIC", now at version v4 (execution time 00:00.040s)
2023.05.19 08:04:23 INFO  time: initialize in 0.65s
2023.05.19 08:04:24 WARN  Build server is not auto-connectable.
2023.05.19 08:05:16 INFO  Shutting down server
2023.05.19 08:05:16 INFO  shutting down Metals
2023.05.19 08:05:16 INFO  Exiting server
