{"_type": "export", "__export_format": 4, "__export_date": "2023-07-12T11:14:06.155Z", "__export_source": "insomnia.desktop.app:v2023.2.2", "resources": [{"_id": "req_30c156c1b85f4dba87069b08fb6c8ae1", "parentId": "fld_259cb847f7cb401dbc64df04b6ed46a3", "modified": 1689160337486, "created": 1689158916541, "url": "{{Server}}/servicio-seguridad/estado", "name": "Check status", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916551.961, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_259cb847f7cb401dbc64df04b6ed46a3", "parentId": "fld_8e0caf4cded34da0a8ccf7aa69543b13", "modified": 1689159064062, "created": 1689158916537, "name": "Checkers", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1689158916552, "_type": "request_group"}, {"_id": "fld_8e0caf4cded34da0a8ccf7aa69543b13", "parentId": "fld_495665faf1ed4b73b90a9016a7135f80", "modified": 1689158916546, "created": 1689158916546, "name": "API v1", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1689158916546, "_type": "request_group"}, {"_id": "fld_495665faf1ed4b73b90a9016a7135f80", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1689158916536, "created": 1689158916536, "name": "<PERSON><PERSON><PERSON>", "description": "", "environment": {"Server": "localhost:8080"}, "environmentPropertyOrder": null, "metaSortKey": -1689158916536, "_type": "request_group"}, {"_id": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "parentId": null, "modified": 1680110699543, "created": 1680110654295, "name": "La Pampa", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "req_c73833a5e6e5405892a255c2436b3882", "parentId": "fld_259cb847f7cb401dbc64df04b6ed46a3", "modified": 1689160339832, "created": 1689158916542, "url": "{{Server}}/servicio-seguridad/logsTest", "name": "Check salidas logs", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916501.961, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_be829b871cfe409c88de0fab757d4f50", "parentId": "fld_b70e6ca91505415ab499001ce4bfeae2", "modified": 1689158916545, "created": 1689158916545, "url": "{{Server}}/servicio-seguridad/api-docs", "name": "Api-Docs", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916545, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_b70e6ca91505415ab499001ce4bfeae2", "parentId": "fld_8e0caf4cded34da0a8ccf7aa69543b13", "modified": 1689159072355, "created": 1689158916544, "name": "Docs", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1689158916549.5, "_type": "request_group"}, {"_id": "req_43e3231929ac4732a33c3523a5aa2e64", "parentId": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "modified": 1689159051910, "created": 1689158916556, "url": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/signup", "name": "credenciales Register Rol Admin", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "application/json", "text": "{\r\n    \"login\": \"23214279819\",// Rol Admin\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1689158916556, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "parentId": "fld_8e0caf4cded34da0a8ccf7aa69543b13", "modified": 1689158916547, "created": 1689158916547, "name": "Por Gateway", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1689158916547, "_type": "request_group"}, {"_id": "req_b26ae8e13c514ef0bd9cfd44c44f715c", "parentId": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "modified": 1689159049533, "created": 1689158916554, "url": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/validarToken", "name": "credenciales validarToken", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "application/json", "text": "{\r\n    \"token\": \"{{Token}}\"\r\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1689158916554, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_e4408e4f6a204ccd9d482b4a14242f26", "parentId": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "modified": 1689159046150, "created": 1689158916552, "url": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/userDetails", "name": "credenciales userDetails", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "application/json", "text": "{\r\n    \"token\": \"23214279819\"\r\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1689158916552, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_56c3cf99153d44d4bf4bb898d6e8159e", "parentId": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "modified": 1689159042799, "created": 1689158916551, "url": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/token", "name": "credenciales getMockToken", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "application/json", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1689158916552, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_6727f56d047144af902fc9067ab5960e", "parentId": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "modified": 1689160120800, "created": 1689158916551, "url": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/login", "name": "credenciales Login", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "application/json", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"id": "pair_9974efe6e80d4214911317ef5bfd99f8", "name": "", "value": "", "description": ""}], "authentication": {}, "metaSortKey": -1689158916551, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_9ce93fe887ac46729aa946dfbb640467", "parentId": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "modified": 1689159019476, "created": 1689158916549, "url": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/findByLogin", "name": "credenciales/findByLogin", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos en dev local al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "application/json", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1689158916549, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_fa989412b2c9412aa09cb6e0715335e2", "parentId": "fld_1ff57e8292dd4f4bb057869fef7a7f39", "modified": 1689159033557, "created": 1689158916548, "url": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/validarSuu", "name": "credenciales validar suu", "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\"", "method": "POST", "body": {"mimeType": "application/json", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1689158916548, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_2871acc213394f26a96cb9995f670efd", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916575, "created": 1689158916575, "url": "localhost:64000/api/test/auth", "name": "credenciales Test auth (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "disabled": false, "token": "{{Token}}", "prefix": ""}, "metaSortKey": -1689158916575, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_fc01ad956c234906abbe5ac5452d5fe8", "parentId": "fld_8e0caf4cded34da0a8ccf7aa69543b13", "modified": 1689159067960, "created": 1689158916557, "name": "Port 64000", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1689158916497, "_type": "request_group"}, {"_id": "req_3b9546fc0ecb4d28b65784c07dca6616", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689159181154, "created": 1689158916574, "url": "localhost:64000/api/test/all", "name": "credenciales Test public (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "GET", "body": {"mimeType": "", "text": ""}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916574, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_16af5ca206de46f5a1b3091f6b9677c6", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689159217828, "created": 1689158916571, "url": "localhost:64000/api/test/pre", "name": "credenciales Test presu (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "GET", "body": {"mimeType": "", "text": ""}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "disabled": false, "token": "{{Token}}", "prefix": ""}, "metaSortKey": -1689158916571, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_1a29724744734f01aaf80f6e69d2b2ad", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916570, "created": 1689158916570, "url": "localhost:64000/api/test/admin", "name": "credenciales Test admin (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "GET", "body": {"mimeType": "", "text": "{\r\n    \"username\": \"23214279819\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"12345678\",\r\n    \"role\": [\r\n        \"mod\",\r\n        \"user\"\r\n    ]\r\n}"}, "parameters": [], "headers": [], "authentication": {"type": "bearer", "disabled": false, "token": "{{Token}}", "prefix": ""}, "metaSortKey": -1689158916570, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_b2fc1fab871e4cebbdf00a856c072c5c", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916569, "created": 1689158916569, "url": "localhost:64000/api/v1/app/credenciales/signin", "name": "credenciales Authenticate MultiRol (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"20280924211\",\r\n    \"password\": \"12345678xq\"\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916569, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_21ff8bfae6674390891f36bec56bcd19", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916569, "created": 1689158916569, "url": "localhost:64000/api/v1/app/credenciales/signup", "name": "credenciales Register (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"23214279819\",// MultiRol 21\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916569, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_04659797660b4295887ad86650cc0f2b", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916568, "created": 1689158916568, "url": "localhost:64000/api/v1/app/credenciales/signin", "name": "credenciales Authenticate Consul (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"20344538485\",\r\n    \"password\": \"12345678xq\"\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916568, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_462e9c5acb1d4c90820948263406c534", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916567, "created": 1689158916567, "url": "localhost:64000/api/v1/app/credenciales/signin", "name": "credenciales Authenticate Presu (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"20390540435\",\r\n    \"password\": \"12345678xq\"\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916567, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_f777f8389c6645b7ae0c600ae92d3793", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916566, "created": 1689158916566, "url": "localhost:64000/api/v1/app/credenciales/signin", "name": "credenciales Authenticate Admin (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916566, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_705dc1e25e0a4eeaa5a1f66c1a5b9b28", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916565, "created": 1689158916565, "url": "localhost:64000/api/v1/app/credenciales/validarToken", "name": "credenciales validarToken (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"token\": \"{{Token}}\"\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916565, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_76054d8bb530424398cc578cba4e246c", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916564, "created": 1689158916564, "url": "localhost:64000/api/v1/app/credenciales/token", "name": "credenciales getToken (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916564, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_20cb271279f04c79abe2d7d0cc797de8", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916563, "created": 1689158916563, "url": "localhost:64000/api/v1/app/credenciales/login", "name": "credenciales Login (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916563, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_c3f4fd291b3f4136b6c40cbeacd64cce", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916562, "created": 1689158916562, "url": "localhost:64000/api/v1/app/credenciales/findByLogin", "name": "credenciales/findByLogin (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"20280924211\",\r\n    \"password\": \"pepepepe\",\r\n    \"roleId\": 11\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916562, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_164c067ec37541f49ba17bf733035024", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916562, "created": 1689158916562, "url": "localhost:64000/api/v1/app/credenciales/findByLogin", "name": "credenciales/findByLogin (port) Copy", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916562, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_84208ff156f14a8d82ef36f7193f1251", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916561, "created": 1689158916561, "url": "localhost:64000/api/v1/app/credenciales/validar", "name": "credenciales validar suu (port)", "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway", "method": "POST", "body": {"mimeType": "", "text": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}"}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916561, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_98319945070d472eb9523dbb0603bdd2", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916560, "created": 1689158916560, "url": "localhost:64000/api/v1/app/credenciales/rolesByLogin", "name": "credenciale rolesByLogin (port) Copy", "description": "", "method": "GET", "body": {}, "parameters": [{"name": "login", "value": "23214279819", "disabled": false}], "headers": [], "authentication": {}, "metaSortKey": -1689158916560, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_524f61387a5d415fafcb5dca5ef42a66", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916559, "created": 1689158916559, "url": "localhost:64000/api/v1/app/credenciales/rolesByLogin", "name": "credenciale rolesByLogin (port)", "description": "", "method": "GET", "body": {}, "parameters": [{"name": "login", "value": "20280924211", "disabled": false}], "headers": [], "authentication": {}, "metaSortKey": -1689158916559, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_0fcc3152cbe148ffbaa8c44994c515d0", "parentId": "fld_fc01ad956c234906abbe5ac5452d5fe8", "modified": 1689158916558, "created": 1689158916558, "url": "localhost:64000/estado", "name": "Check Status (port)", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1689158916558, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_377aedd62cb394240e3118d4069703ede834ad0c", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1680110663857, "created": 1680110663857, "name": "Base Environment", "data": {}, "dataPropertyOrder": null, "color": null, "isPrivate": false, "metaSortKey": 1680110663857, "_type": "environment"}, {"_id": "jar_377aedd62cb394240e3118d4069703ede834ad0c", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1680110663860, "created": 1680110663860, "name": "<PERSON><PERSON><PERSON>", "cookies": [], "_type": "cookie_jar"}, {"_id": "spc_c5811e6091cc4b66af1f37de4aab94bd", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1680110654296, "created": 1680110654296, "fileName": "My Collection", "contents": "", "contentType": "yaml", "_type": "api_spec"}, {"_id": "env_7a9aab036a524ed6b2a3f574481f64c5", "parentId": "env_377aedd62cb394240e3118d4069703ede834ad0c", "modified": 1689159892546, "created": 1682079952139, "name": "La Pampa", "data": {"Token": "xxx"}, "dataPropertyOrder": {"&": ["Token"]}, "color": null, "isPrivate": false, "metaSortKey": 1682079952139, "_type": "environment"}]}