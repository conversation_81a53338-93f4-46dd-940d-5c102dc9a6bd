{"info": {"_postman_id": "a0db232b-3295-4911-a2f8-026fb48b885f", "name": "<PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "14416183"}, "item": [{"name": "Automático", "item": [{"name": "URL con Login Params (usar PROD)", "event": [{"listen": "prerequest", "script": {"exec": ["const paramsString = request.url.split('?')[1];\r", "const eachParamArray = paramsString.split('&');\r", "let params = {};\r", "eachParamArray.forEach((param) => {\r", "    const key = param.split('=')[0];\r", "    const value = param.split('=')[1];\r", "    Object.assign(params, {[key]: value});\r", "});\r", "pm.environment.set(\"Token\", params['token']);\r", "pm.environment.set(\"Refresh\", params['refresh']);\r", "pm.environment.set(\"Expire\", params['expire']);\r", "console.log(pm.environment.get(\"Token\"));\r", "console.log(pm.environment.get(\"Refresh\"));\r", "console.log(pm.environment.get(\"Expire\"));\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://desannsl.lapampa.gob.ar/#/welcome?token=0100faa5-4b02-44f5-a220-3c7574aa40d7!c5acdc4db67a18c57910b4c83aa5f9c3ae691831df66b4b3ceb443277fd026aadff565f7c4f62e&refresh=0017caab4d3da09419390d829efaa23ca51&expire=1200", "protocol": "https", "host": ["desannsl", "<PERSON><PERSON>a", "gob", "ar"], "path": [""], "hash": "/welcome?token=0100faa5-4b02-44f5-a220-3c7574aa40d7!c5acdc4db67a18c57910b4c83aa5f9c3ae691831df66b4b3ceb443277fd026aadff565f7c4f62e&refresh=0017caab4d3da09419390d829efaa23ca51&expire=1200"}, "description": "Aca pegamos el url del navegador cuando estamos en la pagina de seleccion de roles, al enviarlo postman extrae los parametros y los asigna a las variables de entorno para utilizarlos en el resto del backend"}, "response": []}, {"name": "Get Access Token by Test User (1000~1009)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().access_token);\r", "pm.environment.set(\"Refresh\", pm.response.json().refresh_token);\r", "pm.environment.set(\"Expire\", pm.response.json().expires_in);\r", "console.log(pm.environment.get(\"Token\"));\r", "console.log(pm.environment.get(\"Refresh\"));\r", "console.log(pm.environment.get(\"Expire\"));\r", "}\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// Generar un user aleatorio entre 1000 y 1009\r", "pm.globals.set(\"randomUser\", Math.floor(Math.random() * 10) + 1000);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{Token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}, {"key": "scope", "value": "gam_user_data", "type": "text"}, {"key": "username", "value": "{{randomUser}}", "type": "text"}, {"key": "password", "value": "Pass.{{randomUser}}", "type": "text"}, {"key": "authentication_type_name", "value": "local", "type": "text"}, {"key": "initial_properties", "value": "", "type": "text", "disabled": true}, {"key": "repository", "value": "", "type": "text", "disabled": true}, {"key": "request_token_type", "value": "OAuth", "type": "text"}]}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/v2.0/access_token", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "v2.0", "access_token"]}}, "response": []}, {"name": "Get Access Token by Test User 1007", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().access_token);\r", "pm.environment.set(\"Refresh\", pm.response.json().refresh_token);\r", "pm.environment.set(\"Expire\", pm.response.json().expires_in);\r", "console.log(pm.environment.get(\"Token\"));\r", "console.log(pm.environment.get(\"Refresh\"));\r", "console.log(pm.environment.get(\"Expire\"));\r", "}\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// Generar un user aleatorio entre 1000 y 1009\r", "// pm.globals.set(\"randomUser\", Math.floor(Math.random() * 10) + 1000);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{Token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}, {"key": "scope", "value": "gam_user_data", "type": "text"}, {"key": "username", "value": "1007", "type": "text"}, {"key": "password", "value": "Pass.1007", "type": "text"}, {"key": "authentication_type_name", "value": "local", "type": "text"}, {"key": "initial_properties", "value": "", "type": "text", "disabled": true}, {"key": "repository", "value": "", "type": "text", "disabled": true}, {"key": "request_token_type", "value": "OAuth", "type": "text"}]}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/v2.0/access_token", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "v2.0", "access_token"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/rolesByLogin?token={{Token}}", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [{"key": "token", "value": "{{Token}}"}]}}, "response": []}, {"name": "menusByToken -> JWT", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"SessionId\", pm.response.json().objeto.sessionId);\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "pm.environment.set(\"Refresh\", pm.response.json().objeto.refreshToken);\r", "console.log(pm.environment.get(\"Token\"));\r", "console.log(pm.environment.get(\"Refresh\"));\r", "console.log(pm.environment.get(\"Expire\"));\r", "console.log(pm.environment.get(\"SessionId\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid');\r", "pm.environment.set(\"My_uuid\", uuid.v4());\r", "pm.variables.set(\"TimestampMs\", new Date().getTime());\r", "console.log(pm.environment.get(\"My_uuid\")); "], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\",\r\n    \"refresh\": \"{{Refresh}}\",\r\n    \"roleId\": 1,\r\n    \"callbackDate\": {{TimestampMs}},\r\n    \"expiration\": {{Expire}},\r\n    \"uuid\": \"{{My_uuid}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/menusByToken", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "menusByToken"]}, "description": "Este paso es muy importante debemos poner el código de roleId,\n\nAl enviarlo el backend usa los valores de token y refresh del ambiente y crea la Redis session\n\nAl finalizar coloca la sessionId obtenida en las variables del ambiente"}, "response": []}, {"name": "userDetails", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/userDetails", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "userDetails"]}, "description": "Aqui utilizando el token de las variables de ambiente obtiene los detalles del usuario (roles y permisos), si falla es porque la session caduco en GAM y/o Redis caido"}, "response": []}, {"name": "userinfo", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/userinfo?token={{Token}}", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "userinfo"], "query": [{"key": "token", "value": "{{Token}}"}]}, "description": "Obtiene los datos del usuario en GAM si vienen null es porque expiró"}, "response": []}, {"name": "validarToken", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/validarToken", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "validarToken"]}, "description": "Valida el token si da true es válido si es false ya expiró"}, "response": []}, {"name": "refresh", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.access_token);\r", "pm.environment.set(\"Refresh\", pm.response.json().objeto.refresh_token);\r", "pm.environment.set(\"Expire\", pm.response.json().objeto.expires_in);\r", "console.log(pm.environment.get(\"Token\"));\r", "console.log(pm.environment.get(\"Refresh\"));\r", "console.log(pm.environment.get(\"Expire\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/refresh?refresh_token={{Refresh}}", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "refresh"], "query": [{"key": "refresh_token", "value": "{{Refresh}}"}]}, "description": "Genera nuevo token y refresh token y los actualiza en el ambiente"}, "response": []}, {"name": "signout <PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/signoutJson?token={{Token}}", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "signout<PERSON><PERSON>"], "query": [{"key": "token", "value": "{{Token}}"}]}, "description": "Con el token de las variables de ambiente procede a cerrar la sesion en GAM y a borra la sesion en Redis,. si falla es probale que la session gam este expirada y/o el redis caido"}, "response": []}, {"name": "menusByToken -> GAM (a deprecar)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"SessionId\", pm.response.json().objeto.sessionId);\r", "console.log(pm.environment.get(\"SessionId\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid');\r", "pm.environment.set(\"My_uuid\", uuid.v4());\r", "pm.variables.set(\"TimestampMs\", new Date().getTime());\r", "console.log(pm.environment.get(\"My_uuid\")); "], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\",\r\n    \"refresh\": \"{{Refresh}}\",\r\n    \"roleId\": 1,\r\n    \"callbackDate\": {{TimestampMs}},\r\n    \"expiration\": {{Expire}},\r\n    \"uuid\": \"{{My_uuid}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/menusByToken", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "menusByToken"]}, "description": "Este paso es muy importante debemos poner el código de roleId,\n\nAl enviarlo el backend usa los valores de token y refresh del ambiente y crea la Redis session\n\nAl finalizar coloca la sessionId obtenida en las variables del ambiente"}, "response": []}, {"name": "findByLogin (Deprecado)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"SessionId\", pm.response.json().objeto.sessionId);\r", "console.log(pm.environment.get(\"SessionId\"));\r", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{To<PERSON>}}\",\r\n    \"refresh\": \"{{Refresh}}\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/findByLogin", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "findByLogin"]}, "description": "Este paso es muy importante debemos poner el código de roleId,\n\nAl enviarlo el backend usa los valores de token y refresh del ambiente y crea la Redis session\n\nAl finalizar coloca la sessionId obtenida en las variables del ambiente"}, "response": []}], "description": "Utiliza las variables del environment La Pampa para ejecutar todas las acciones en secuencia"}, {"name": "Checkers", "item": [{"name": "Check status", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/estado", "host": ["{{Server}}"], "path": ["servicio-seguridad", "estado"]}}, "response": []}, {"name": "Check salidas logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/logsTest", "host": ["{{Server}}"], "path": ["servicio-seguridad", "logsTest"]}}, "response": []}, {"name": "Redis Cleanup", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/redis/cleanup", "host": ["{{Server}}"], "path": ["servicio-seguridad", "redis", "cleanup"]}}, "response": []}, {"name": "Check actuator", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/actuator", "host": ["{{Server}}"], "path": ["servicio-seguridad", "actuator"]}}, "response": []}, {"name": "Check actuator LogFile", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/actuator/logfile", "host": ["{{Server}}"], "path": ["servicio-seguridad", "actuator", "logfile"]}}, "response": []}]}, {"name": "Docs", "item": [{"name": "Api-Docs", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/v3/api-docs", "host": ["{{Server}}"], "path": ["servicio-seguridad", "v3", "api-docs"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>wagger", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/swagger-ui/index.html", "host": ["{{Server}}"], "path": ["servicio-seguridad", "swagger-ui", "index.html"]}}, "response": []}]}, {"name": "API v1 (DEPRECADO)", "item": [{"name": "Por Gateway", "item": [{"name": "credenciales Login Desa", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"jqXkZ#jeX0.\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/login", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "login"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Login Local", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"jqXkZ#jeX0.\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/login", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "login"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales validar suu", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"jqXkZ#jeX0.\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TestServer}}/servicio-seguridad/api/v1/app/credenciales/validarSuu", "host": ["{{TestServer}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "validarSuu"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "credenciales/findByLogin", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"jqXkZ#jeX0.\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/findByLogin", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "findByLogin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos en dev local al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales getMockToken", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/token", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "token"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales userDetails", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"23214279819\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{DesaServer}}/servicio-seguridad/api/v1/app/credenciales/userDetails", "host": ["{{DesaServer}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "userDetails"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"23214279819\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales validarToken", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v1/app/credenciales/validarToken", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "validarToken"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Register Rol Admin", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",// R<PERSON> Admin\r\n    \"password\": \"jqXkZ#jeX0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{DesaServer}}/servicio-seguridad/api/v1/app/credenciales/signup", "host": ["{{DesaServer}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "signup"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",// Rol Admin\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciale rolesByLogin", "request": {"method": "GET", "header": [], "url": {"raw": "{{DesaServer}}/servicio-seguridad/api/v1/app/credenciales/rolesByLogin?login=20290740305", "host": ["{{DesaServer}}"], "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [{"key": "login", "value": "20290740305"}]}}, "response": []}]}, {"name": "Port 64000", "item": [{"name": "Check Status (port)", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:64000/estado", "host": ["localhost"], "port": "64000", "path": ["estado"]}}, "response": []}, {"name": "credenciale rolesByLogin (port)", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:64000/api/v1/app/credenciales/rolesByLogin?login=20280924211", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [{"key": "login", "value": "20280924211"}]}}, "response": []}, {"name": "credenciale rolesByLogin (port) Copy", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:64000/api/v1/app/credenciales/rolesByLogin?login=23214279819", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [{"key": "login", "value": "23214279819"}]}}, "response": []}, {"name": "credenciales validar suu (port)", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales/findByLogin (port)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"20280924211\",\r\n    \"password\": \"pepepepe\",\r\n    \"roleId\": 11\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/findByLogin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "findByLogin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales/findByLogin (port) Copy", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"20290740305\",// <PERSON><PERSON>\r\n    \"password\": \"pepepepe\",\r\n    \"roleId\": 11\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/findByLogin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "findByLogin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales/findByLogin (port) Copy", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/findByLogin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "findByLogin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales/Login (port) DESA", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8080/servicio-seguridad/api/v1/app/credenciales/login", "protocol": "http", "host": ["10", "5", "42", "24"], "port": "8080", "path": ["servicio-seguridad", "api", "v1", "app", "credenciales", "login"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales/findByLogin (port) Copy 2", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/findByLogin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "findByLogin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Login (port)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/login", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "login"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales getToken (port)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/token", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "token"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales validarToken (port)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validarToken", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validarToken"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Authenticate Admin (port)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/signin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "signin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Authenticate Presu (port)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"20390540435\",\r\n    \"password\": \"12345678xq\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/signin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "signin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Authenticate Consul (port)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"20344538485\",\r\n    \"password\": \"12345678xq\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/signin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "signin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Authenticate MultiRol (port)", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"Token\", pm.response.json().objeto.token);\r", "console.log(pm.environment.get(\"Token\"));\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"20280924211\",\r\n    \"password\": \"12345678xq\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/signin", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "signin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Register (port)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",// MultiRol 21\r\n    \"password\": \"xqMkZUxeM0\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/signup", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "signup"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Test admin (port)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"23214279819\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"12345678\",\r\n    \"role\": [\r\n        \"mod\",\r\n        \"user\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/test/admin", "host": ["localhost"], "port": "64000", "path": ["api", "test", "admin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Test presu (port)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"23214279819\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"12345678\",\r\n    \"role\": [\r\n        \"mod\",\r\n        \"user\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/test/pre", "host": ["localhost"], "port": "64000", "path": ["api", "test", "pre"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Test Consul <PERSON><PERSON> (port)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"20344538485\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"12345678xq\",\r\n    \"role\": [\r\n        \"mod\",\r\n        \"user\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/test/user", "host": ["localhost"], "port": "64000", "path": ["api", "test", "user"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Test public (port)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"23214279819\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"12345678\",\r\n    \"role\": [\r\n        \"mod\",\r\n        \"user\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/test/all", "host": ["localhost"], "port": "64000", "path": ["api", "test", "all"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales Test auth (port)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/test/auth", "host": ["localhost"], "port": "64000", "path": ["api", "test", "auth"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23214279819\",\r\n    \"password\": \"xqMkZUxeM0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}]}]}, {"name": "API v2", "item": [{"name": "Por Gateway", "item": [{"name": "Manual", "item": [{"name": "[Gam Seguridad Controller] menusByToken", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"0100faa5-4b02-44f5-a220-3c7574aa40d7!928bc583fb4edf77210a1f465f5cd0f5ededd20f2c017352d16fcf3fe79ddda2c0f7a87339bbf1\",\r\n    \"refresh\": \"00146c28e90e0f84ea7a5394fecb685b70f\",\r\n    \"roleId\": 1,\r\n    \"callbackDate\": 1709325503384,\r\n    \"expiration\": 1200\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/findByLogin", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "findByLogin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos en dev local al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"0100faa5-4b02-44f5-a220-3c7574aa40d7!245ff6bdcaafbd7301b3a5216c51ac0d556dc6229a7f1a2b4477ab614cf9a1dba866622cdf2005\",\r\n    \"refresh\": \"0012b37beccdfd7422c8e2327b4bf58bc78\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "[Gam Seguridad Controller] userDetails", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"0100faa5-4b02-44f5-a220-3c7574aa40d7!a2630f971846ebaf36c961dce2dde54e7a8b2e2818a37f283b9f564c4940c1930139c02ae53d2b\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/userDetails", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "userDetails"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"23214279819\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "credenciales validarToken", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/validarToken", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "validarToken"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validarToken", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{Token}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/validarToken", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "validarToken"]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": ""}]}, {"name": "credenciales serviceNameByToken", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{To<PERSON>}}\",\r\n    \"serviceName\": \"servicio-dummy_1.0.0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/serviceNameByToken", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "serviceNameByToken"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": []}, {"name": "[<PERSON><PERSON>gu<PERSON>d Controller] rolesByLogin", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/rolesByLogin?token={{Token}}", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [{"key": "token", "value": "{{Token}}"}]}}, "response": []}, {"name": "[Gam Controller] redirect login", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/login", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "login"]}}, "response": []}, {"name": "[Gam Controller] userinfo", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/userinfo?token=0100faa5-4b02-44f5-a220-3c7574aa40d7!43aa35a7f98ccb011a550b15f0eff832eb3c815c4c92c8b0d1a6b1e8edeb785f46b5274ea45ece", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "userinfo"], "query": [{"key": "token", "value": "0100faa5-4b02-44f5-a220-3c7574aa40d7!43aa35a7f98ccb011a550b15f0eff832eb3c815c4c92c8b0d1a6b1e8edeb785f46b5274ea45ece"}]}}, "response": []}, {"name": "[Gam Controller] refresh", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/refresh?refresh_token=002a0d8699d6b484b2b9ffb2d1be4043e77", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "refresh"], "query": [{"key": "refresh_token", "value": "002a0d8699d6b484b2b9ffb2d1be4043e77"}]}}, "response": []}, {"name": "[Gam Controller] signout <PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/signoutJson?token=0100faa5-4b02-44f5-a220-3c7574aa40d7!5569c6019178e26aaa88b0116dabfc6e45993406fa25de5b3b40974f3c69bbf6403e6ebe3d9f19", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "signout<PERSON><PERSON>"], "query": [{"key": "token", "value": "0100faa5-4b02-44f5-a220-3c7574aa40d7!5569c6019178e26aaa88b0116dabfc6e45993406fa25de5b3b40974f3c69bbf6403e6ebe3d9f19"}]}}, "response": []}, {"name": "[Gam Controller] redirect signout (Deprecado)", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-seguridad/oauth/gam/signout?token=0100faa5-4b02-44f5-a220-3c7574aa40d7!8d929d9329f479c41cb0a425e70ce091f8e13463749fd0cda6698c6f260202a70eb9f85d6a3815&sessionId=-1227686131789303600", "host": ["{{Server}}"], "path": ["servicio-seguridad", "o<PERSON>h", "gam", "signout"], "query": [{"key": "token", "value": "0100faa5-4b02-44f5-a220-3c7574aa40d7!8d929d9329f479c41cb0a425e70ce091f8e13463749fd0cda6698c6f260202a70eb9f85d6a3815"}, {"key": "sessionId", "value": "-1227686131789303600"}]}}, "response": []}, {"name": "[Gam Seguridad Controller] findByLogin (Deprecado)", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"0100faa5-4b02-44f5-a220-3c7574aa40d7!928bc583fb4edf77210a1f465f5cd0f5ededd20f2c017352d16fcf3fe79ddda2c0f7a87339bbf1\",\r\n    \"refresh\": \"00146c28e90e0f84ea7a5394fecb685b70f\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/app/credenciales/findByLogin", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "app", "credenciales", "findByLogin"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos en dev local al no pasar por el servicio gateway"}, "response": [{"name": "credenciales validar Eje<PERSON>lo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"0100faa5-4b02-44f5-a220-3c7574aa40d7!245ff6bdcaafbd7301b3a5216c51ac0d556dc6229a7f1a2b4477ab614cf9a1dba866622cdf2005\",\r\n    \"refresh\": \"0012b37beccdfd7422c8e2327b4bf58bc78\",\r\n    \"roleId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/credenciales/validar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "credenciales", "validar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}]}]}, {"name": "Genexus API", "item": [{"name": "Demo", "item": [{"name": "Api v2.0 Get Access Token", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"gamToken\", pm.response.json().access_token);\r", "pm.environment.set(\"gamRefresh\", pm.response.json().refresh_token);\r", "pm.environment.set(\"gamExpire\", pm.response.json().expires_in);\r", "console.log(pm.environment.get(\"gamToken\"));\r", "console.log(pm.environment.get(\"gamRefresh\"));\r", "console.log(pm.environment.get(\"gamExpire\"));\r", "}\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{Token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}, {"key": "scope", "value": "gam_user_data", "type": "text"}, {"key": "username", "value": "1009", "type": "text"}, {"key": "password", "value": "Pass.1009", "type": "text"}, {"key": "authentication_type_name", "value": "local", "type": "text"}, {"key": "initial_properties", "value": "", "type": "text"}, {"key": "repository", "value": "", "type": "text"}, {"key": "request_token_type", "value": "OAuth", "type": "text"}]}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/v2.0/access_token", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "v2.0", "access_token"]}}, "response": []}, {"name": "Api v2.0 Get UserInfo", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{gamToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/v2.0/userinfo", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "v2.0", "userinfo"]}}, "response": []}, {"name": "Api v2.0 Get Refresh", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"gamToken\", pm.response.json().access_token);\r", "pm.environment.set(\"gamRefresh\", pm.response.json().refresh_token);\r", "pm.environment.set(\"gamExpire\", pm.response.json().expires_in);\r", "console.log(pm.environment.get(\"gamToken\"));\r", "console.log(pm.environment.get(\"gamRefresh\"));\r", "console.log(pm.environment.get(\"gamExpire\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{Token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263", "type": "text"}, {"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "refresh_token", "value": "{{gamRefresh}}", "type": "text"}]}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/v2.0/access_token", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "v2.0", "access_token"]}}, "response": []}, {"name": "UserInfo Demo", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{gamToken}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/userinfo", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "userinfo"]}}, "response": []}, {"name": "Signout <PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "url": {"raw": "{{GenexusDemo}}/oauth/gam/signout?oauth=signout&client_id=8dadf0ee895f4d04886f600d642b82f1&client_secret=df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263&server_ip=1&redirect_uri=http://testnnsl.lapampa.gob.ar/&token={{Token}}&first_call=1&state=state", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "signout"], "query": [{"key": "o<PERSON>h", "value": "signout"}, {"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1"}, {"key": "client_secret", "value": "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263"}, {"key": "server_ip", "value": "1"}, {"key": "redirect_uri", "value": "http://testnnsl.lapampa.gob.ar/"}, {"key": "token", "value": "{{Token}}"}, {"key": "first_call", "value": "1"}, {"key": "state", "value": "state"}, {"key": "repository_id", "value": "", "disabled": true}]}}, "response": []}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263", "type": "text"}, {"key": "refresh_token", "value": "00166838140557a40b2823ee3612f25f669", "type": "text"}]}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/access_token", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "access_token"]}}, "response": []}, {"name": "User Exists Demo", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "user", "value": "1000", "type": "text"}]}, "url": {"raw": "{{GenexusDemo}}/oauth/gam/exists", "host": ["{{GenexusDemo}}"], "path": ["o<PERSON>h", "gam", "exists"]}}, "response": []}]}, {"name": "Prod", "item": [{"name": "Api v2.0 Get Access Token", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"gamToken\", pm.response.json().access_token);\r", "pm.environment.set(\"gamRefresh\", pm.response.json().refresh_token);\r", "pm.environment.set(\"gamExpire\", pm.response.json().expires_in);\r", "console.log(pm.environment.get(\"gamToken\"));\r", "console.log(pm.environment.get(\"gamRefresh\"));\r", "console.log(pm.environment.get(\"gamExpire\"));\r", "}\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{Token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "e4c0590974e14999ad5aa8384810ad1e46666052d75341e3bc70459ab0b1daad0ce927bf", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}, {"key": "scope", "value": "gam_user_data", "type": "text"}, {"key": "username", "value": "1000", "type": "text"}, {"key": "password", "value": "Pass.1000", "type": "text"}, {"key": "authentication_type_name", "value": "local", "type": "text"}, {"key": "initial_properties", "value": "", "type": "text"}, {"key": "repository", "value": "", "type": "text"}, {"key": "request_token_type", "value": "OAuth", "type": "text"}]}, "url": {"raw": "{{GenexusProd}}/oauth/gam/v2.0/access_token", "host": ["{{GenexusProd}}"], "path": ["o<PERSON>h", "gam", "v2.0", "access_token"]}}, "response": []}, {"name": "Api v2.0 Get UserInfo", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{gamToken}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{GenexusProd}}/oauth/gam/v2.0/userinfo", "host": ["{{GenexusProd}}"], "path": ["o<PERSON>h", "gam", "v2.0", "userinfo"]}}, "response": []}, {"name": "Api v2.0 Get Refresh", "event": [{"listen": "test", "script": {"exec": ["if(pm.response.code === 200){\r", "pm.environment.set(\"gamToken\", pm.response.json().access_token);\r", "pm.environment.set(\"gamRefresh\", pm.response.json().refresh_token);\r", "pm.environment.set(\"gamExpire\", pm.response.json().expires_in);\r", "console.log(pm.environment.get(\"gamToken\"));\r", "console.log(pm.environment.get(\"gamRefresh\"));\r", "console.log(pm.environment.get(\"gamExpire\"));\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{Token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "df0e9710e5454bac81db8b090ded59315056475373d7552c8c3d4163926bbc6f2d3e6263", "type": "text"}, {"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "refresh_token", "value": "{{gamRefresh}}", "type": "text"}]}, "url": {"raw": "{{GenexusProd}}/oauth/gam/v2.0/access_token", "host": ["{{GenexusProd}}"], "path": ["o<PERSON>h", "gam", "v2.0", "access_token"]}}, "response": []}, {"name": "UserInfo Prod", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "0100faa5-4b02-44f5-a220-3c7574aa40d7!9bf5da01a4f43449cef8549296d98ea730a123d6ae504e5e3d506cbb01f1fe4f7746d3eaecceae", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{GenexusProd}}/oauth/gam/userinfo", "host": ["{{GenexusProd}}"], "path": ["o<PERSON>h", "gam", "userinfo"]}}, "response": []}, {"name": "Signout Prod", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "url": {"raw": "{{GenexusProd}}/oauth/gam/signout?oauth=signout&client_id=8dadf0ee895f4d04886f600d642b82f1&client_secret=e4c0590974e14999ad5aa8384810ad1e46666052d75341e3bc70459ab0b1daad0ce927bf&server_ip=1&redirect_uri=http://testnnsl.lapampa.gob.ar/&token=0100faa5-4b02-44f5-a220-3c7574aa40d7!82a668ab205acffeccb5429201437a2ff63332c05c760f3c9a0f5f738f8e98d1e14b8059825693&first_call=1&state=state", "host": ["{{GenexusProd}}"], "path": ["o<PERSON>h", "gam", "signout"], "query": [{"key": "o<PERSON>h", "value": "signout"}, {"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1"}, {"key": "client_secret", "value": "e4c0590974e14999ad5aa8384810ad1e46666052d75341e3bc70459ab0b1daad0ce927bf"}, {"key": "server_ip", "value": "1"}, {"key": "redirect_uri", "value": "http://testnnsl.lapampa.gob.ar/"}, {"key": "token", "value": "0100faa5-4b02-44f5-a220-3c7574aa40d7!82a668ab205acffeccb5429201437a2ff63332c05c760f3c9a0f5f738f8e98d1e14b8059825693"}, {"key": "first_call", "value": "1"}, {"key": "state", "value": "state"}, {"key": "repository_id", "value": "", "disabled": true}]}}, "response": []}, {"name": "Refresh Prod", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "client_secret", "value": "e4c0590974e14999ad5aa8384810ad1e46666052d75341e3bc70459ab0b1daad0ce927bf", "type": "text"}, {"key": "refresh_token", "value": "00320a33ff8ae264b1daef0fc0d8612814c", "type": "text"}]}, "url": {"raw": "{{GenexusProd}}/oauth/gam/access_token", "host": ["{{GenexusProd}}"], "path": ["o<PERSON>h", "gam", "access_token"]}}, "response": []}, {"name": "User Exists Prod", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "8dadf0ee895f4d04886f600d642b82f1", "type": "text"}, {"key": "user", "value": "23214279819", "type": "text"}]}, "url": {"raw": "{{GenexusProd}}/oauth/gam/exists", "host": ["{{GenexusProd}}"], "path": ["o<PERSON>h", "gam", "exists"]}}, "response": []}]}]}]}, {"name": "LOGS USUARIO", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": 1009,\r\n    \"tipo\": \"TEST\",\r\n    \"estado\": \"403\",\r\n    \"resultado\": \"{\\\"error\\\":\\\"<PERSON><PERSON><PERSON>\\\"}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/userLogs/crear", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "userLogs", "crear"]}}, "response": []}, {"name": "Listar Logs", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"\",\r\n    \"tipo\": \"TEST\", // filtrar por ejemplo TEST o FORBIDDEN\r\n    \"estado\": \"\",\r\n    \"creado\": \"\",\r\n    \"resultado\": \"\",\r\n    \"desde\": \"\",\r\n    \"hasta\": \"\",\r\n    \"size\": 10,\r\n    \"page\": 0,\r\n    \"sortBy\": \"creado\",\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-seguridad/api/v2/userLogs/listar", "host": ["{{Server}}"], "path": ["servicio-seguridad", "api", "v2", "userLogs", "listar"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "DesaServer", "value": "https://desannslapi.lapampa.gob.ar", "type": "string"}, {"key": "ProdServer", "value": "https://nnslapi.lapampa.gob.ar", "type": "string"}, {"key": "TestServer", "value": "https://testnnslapi.lapampa.gob.ar", "type": "string"}, {"key": "Security", "value": "localhost:64007", "type": "string"}, {"key": "Server", "value": "localhost:8080", "type": "string"}, {"key": "GenexusDemo", "value": "https://gxdemo5.lapampa.gob.ar/suugam", "type": "string"}, {"key": "GenexusProd", "value": "https://suu.lapampa.gob.ar/suugam", "type": "string"}, {"key": "gamToken", "value": "", "type": "string"}, {"key": "gamRefresh", "value": "", "type": "string"}, {"key": "gamExpire", "value": "", "type": "string"}]}