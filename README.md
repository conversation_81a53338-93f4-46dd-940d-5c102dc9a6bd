
# NSL Servicio Seguridad

Este servicio brinda la generacion de tokens y datos del usuario para la validacion de peticiones del gateway NSL;


## Ejecución

Es necesario ejecutar un build del proyecto (incluso despues de hacer pull que introducen cambios) pues incluye **código autogenerado** en implementación de mappers, clases de entidades Q (QueryDsl) y clases del cliente soap
(paquete client.gen) procesos ya preconfigurados en el pom.xml

En Eclipse hacemos **maven -> build...**

y luego como **goal** ingresamos **clean install -DskipTests**

nombramos este run como **nsl-servicio-seguridad SkipTest**

y lo tendremos disponible para ejecutar nuestros builds desde el menu.

Para por ejemplo obtener las clases generadas por lombok podemos de la misma forma
activar el perfil de su build

haciendo **maven -> build...**

y luego como **goal** ingresamos **clean lombok:delombok -Pdlombok**

nombramos este run como **nsl-servicio-seguridad Delombok**

Es necesario al menos el servicio de descubrimiento (Eureka) corriendo por detrás.

También durante el desarrollo (y prod) es recomendable establecer el logging level root en ERROR para conservar recursos y poner en DEBUG solo el servicio que estemos trabajando. Ver bootstrap.yaml 

```code
logging:
 level: 
  root: ERROR # DEBUG o INFO
```
Estos ajustes por entorno estan embebidos en un unico yml al final en secciones

```code
---
spring:
  config:
    activate:
      on-profile: "test"
  datasource:
    url: "*************************************"
```
    
Así para cambiar el profile se coloca el argumento jvm

-Dspring.profiles.active=[ambiente]

por ejemplo para test

-Dspring.profiles.active=test

**Importante:** Si se omite el ajuste se toma desa como entorno por defecto

Si consultamos el estado del servicio tendremos esta respuesta con el perfil elegido

```
Servicio Seguridad: Ok
Project version: 1.0.1
Profile activo: test  <---
Pool Name: HikariPool-seguridad
Java version: 17.0.7
DB User: LAPAMPA
Spring Boot version: 3.1.4
```

## Contribuyentes

- Equipo de desarrollo `CeSiDa` La Pampa
- Equipo de desarrollo `Be The Driver`


## Licencia propietaria

&copy; La Pampa 2023 - Todos los Derechos Reservados
